#!/usr/bin/env python3
"""
Startup script for the DMP Generator Web Application
"""
import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """Check if Python version is 3.11 or higher"""
    if sys.version_info < (3, 11):
        print("❌ Error: Python 3.11 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True

def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    
    # Install webapp dependencies
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "webapp_requirements.txt"])
        print("✅ Web app dependencies installed")
    except subprocess.CalledProcessError:
        print("❌ Failed to install web app dependencies")
        return False
    
    # Install madmpy in development mode
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-e", "."])
        print("✅ madmpy installed in development mode")
    except subprocess.CalledProcessError:
        print("❌ Failed to install madmpy")
        return False
    
    return True

def check_files():
    """Check if required files exist"""
    required_files = [
        "webapp/app.py",
        "webapp/templates/base.html",
        "webapp/templates/index.html",
        "webapp/templates/create_dmp.html",
        "webapp/templates/examples.html",
        "src/madmpy/__init__.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ Missing required files:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    
    print("✅ All required files found")
    return True

def run_webapp():
    """Run the Flask web application"""
    print("🚀 Starting DMP Generator Web Application...")
    print("📍 Application will be available at: http://localhost:5000")
    print("🛑 Press Ctrl+C to stop the server")
    print("-" * 50)
    
    # Change to webapp directory and run the app
    os.chdir("webapp")
    try:
        subprocess.run([sys.executable, "app.py"])
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except Exception as e:
        print(f"❌ Error running application: {e}")

def main():
    """Main function"""
    print("🔧 DMP Generator Web Application Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Check required files
    if not check_files():
        print("\n💡 Make sure you're running this script from the madmpy root directory")
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        sys.exit(1)
    
    print("\n🎉 Setup complete!")
    print("-" * 50)
    
    # Ask user if they want to start the app
    try:
        response = input("Start the web application now? (y/n): ").lower().strip()
        if response in ['y', 'yes']:
            run_webapp()
        else:
            print("\n📝 To start the application later, run:")
            print("   cd webapp")
            print("   python app.py")
    except KeyboardInterrupt:
        print("\n👋 Setup completed. Run the app manually when ready.")

if __name__ == "__main__":
    main()
