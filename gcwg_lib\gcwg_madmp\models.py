"""
Advanced data models for gcwg_madmp
"""

from pydantic import BaseModel, Field, validator, root_validator
from typing import Optional, List, Dict, Any, Union
from enum import Enum
from datetime import date
from datetime import datetime
from pydantic import AnyUrl
from pydantic import EmailStr
from typing import List, Optional
from typing import Optional
from .enums import *


class Dmp(BaseModel):
    """
    Generated class for DMP
    """

    general_info: Optional[DmpGeneralInfo] = None
    contributor: Optional[List[DmpContributorItem]] = Field(min_items=1)
    # Approval of the maDMP
    approval: DmpApproval = Field(description="Approval of the maDMP")
    project: Optional[List[DmpProjectItem]] = None
    cost: Optional[List[DmpCostItem]] = None
    # Indigenous considerations related to the maDMP or the data.
    indigenous_considerations: DmpIndigenousConsiderations = Field(description="Indigenous considerations related to the maDMP or the data.")
    dataset: Optional[List[DmpDatasetItem]] = Field(min_items=1)
    # Specifies the party which can provide information about the DMP. This is not necessarily the DMP ...
    contact: DmpContact = Field(description="Specifies the party which can provide information about the DMP. This is not necessarily the DMP creator, and it can be a person or an organization.")


    @validator('contributor')
    def validate_contributor(cls, v):
        """Validate contributor"""
        # Add custom validation logic here
        return v

    @validator('approval')
    def validate_approval(cls, v):
        """Validate approval"""
        # Add custom validation logic here
        return v

    @validator('indigenous_considerations')
    def validate_indigenous_considerations(cls, v):
        """Validate indigenous_considerations"""
        # Add custom validation logic here
        return v

    @validator('dataset')
    def validate_dataset(cls, v):
        """Validate dataset"""
        # Add custom validation logic here
        return v

class Dmpapproval(BaseModel):
    """
    Approval of the maDMP
    """

    # Approval status for the DMP
    status: Enum10 = Field(description="Approval status for the DMP", example="approved")
    # To provide any free-form text information on the approval for the DMP
    description: Optional[str] = Field(description="To provide any free-form text information on the approval for the DMP", example="Need to respond to all required fields")
    # Email of the person who approved the maDMP
    by_mbox: Optional[EmailStr] = Field(description="Email of the person who approved the maDMP")


    @root_validator
    def validate_conditional_0(cls, values):
        """Validate conditional schema 0"""
        # Add conditional validation logic here
        return values

    @root_validator
    def validate_conditional_1(cls, values):
        """Validate conditional schema 1"""
        # Add conditional validation logic here
        return values

    @root_validator
    def validate_conditional_2(cls, values):
        """Validate conditional schema 2"""
        # Add conditional validation logic here
        return values

class Dmpcontact(BaseModel):
    """
    Specifies the party which can provide information about the DMP. This is not necessarily the DMP creator, and it can be a person or an organization.
    """

    # Name of the contact person
    name: str = Field(description="Name of the contact person", example="Winnie Pooh")
    # E-mail address
    mbox: EmailStr = Field(description="E-mail address", example="<EMAIL>")
    # Persistent identifier associated with the contact
    contact_id: DmpcontactContactId = Field(description="Persistent identifier associated with the contact")
    # Role of the reponsible party.
    role: Optional[Enum77] = Field(description="Role of the reponsible party.", example="Contact")
    # On-line information that can be used to contact the responsible individual or organization. This ...
    url: Optional[AnyUrl] = Field(description="On-line information that can be used to contact the responsible individual or organization. This element should be expressed as a URL that will guide the user to further information online.  ", example="www.geogratis.gc.ca")
    # Organization the contact person is affiliated to. For Canadian Departments and agencies, conform ...
    organization: Optional[str] = Field(description="Organization the contact person is affiliated to. For Canadian Departments and agencies, conform with http://www.tbs-sct.gc.ca/fip-pcim/reg-eng.asp. Sub and sub-sub organization (sectors, branches, et", example="Government of Canada; Natural Resources Canada; Earth Sciences Sector; Canada Centre for Mapping and Earth Observation ")
    # Position of the contact if contact is a person.
    position: Optional[str] = Field(description="Position of the contact if contact is a person.", example="Senior Systems Scientist")
    telephone: Optional[List[float]] = None
    fax: Optional[List[float]] = None
    # Enter the street address for the responsible organization or individual
    delivery_point: Optional[str] = Field(description="Enter the street address for the responsible organization or individual ", example="2144; King West")
    # City of the location of the responsible organization or individual
    city: Optional[str] = Field(description="City of the location of the responsible organization or individual ", example="Sherbrooke")
    # Province or state of the location. For Canadian Provinces and Territories use the Data reference ...
    province_state: Optional[DmpcontactProvinceState] = Field(description="Province or state of the location. For Canadian Provinces and Territories use the Data reference standard on Canadian provinces and territories  ")
    # ZIP or other postal code of the location
    postal_zip_code: Optional[str] = Field(description="ZIP or other postal code of the location ", example="J1J 2E8")
    # Country of the organization the contact is affiliated to
    country: Optional[DmpcontactCountry] = Field(description="Country of the organization the contact is affiliated to")
    # The time period (including time zone) when the organization or individual can be contacted
    hours_of_service: Optional[str] = Field(description="The time period (including time zone) when the organization or individual can be contacted ", example="8:30/12:00; 13:00/16:30 EST")


    @validator('contact_id')
    def validate_contact_id(cls, v):
        """Validate contact_id"""
        # Add custom validation logic here
        return v

class Dmpcontributoritem(BaseModel):
    """
    Party involved in the process of data management described by the DMP, or party involved in the creation and management of the DMP itself.
    """

    # Name
    name: str = Field(description="Name", example="Christopher Robin")
    role: Optional[List[str]] = Field(min_items=1)
    # Mail address
    mbox: Optional[EmailStr] = Field(description="Mail address", example="<EMAIL>")
    # Identifier of the contributor
    contributor_id: DmpcontributoritemContributorId = Field(description="Identifier of the contributor")
    affiliation: Optional[List[DmpcontributoritemAffiliationItem]] = Field(min_items=1)
    # Role or position of the contributor or cited responsible party
    position: Optional[str] = Field(description="Role or position of the contributor or cited responsible party")
    telephone: Optional[List[float]] = None
    fax: Optional[List[float]] = None
    # Enter the street address for the contributor or the cited responsible party
    delivery_point: Optional[str] = Field(description="Enter the street address for the contributor or the cited responsible party")
    # City of the location of the contributor or the cited responsible party
    city: Optional[str] = Field(description="City of the location of the contributor or the cited responsible party")
    # ZIP or other postal code of the location
    postal_zip_code: Optional[str] = Field(description="ZIP or other postal code of the location")
    # On-line information that can be used to contact the contributor or the cited responsible party. T...
    url: Optional[AnyUrl] = Field(description="On-line information that can be used to contact the contributor or the cited responsible party. This element should be expressed as a URL that will guide the user to further information online.  ")
    # The time period (including time zone) when the contributor or the cited responsible party can be ...
    hours_of_service: Optional[str] = Field(description="The time period (including time zone) when the contributor or the cited responsible party can be contacted ")


    @validator('role')
    def validate_role(cls, v):
        """Validate role"""
        # Add custom validation logic here
        return v

    @validator('contributor_id')
    def validate_contributor_id(cls, v):
        """Validate contributor_id"""
        # Add custom validation logic here
        return v

    @validator('affiliation')
    def validate_affiliation(cls, v):
        """Validate affiliation"""
        # Add custom validation logic here
        return v

class Dmpcostitem(BaseModel):
    """
    To list costs related to data management. Providing multiple instances of a 'Cost' allows to break down costs into details. Providing one 'Cost' instance allows to provide one aggregated sum.
    """

    # Title
    title: str = Field(description="Title", example="Cloud storage and software")
    # Description
    description: Optional[str] = Field(description="Description", example="Costs for running the project.")
    # Value
    value: Optional[float] = Field(description="Value", example="1,000,000")
    cost_documentation: Optional[List[DmpcostitemCostDocumentationItem]] = None


    @root_validator
    def validate_conditional_0(cls, values):
        """Validate conditional schema 0"""
        # Add conditional validation logic here
        return values

class Dmpdatasetitem(BaseModel):
    """
    To describe data on a non-technical level.
    """

    # Title is a property in both Dataset and Distribution, in compliance with W3C DCAT. In some cases ...
    title: str = Field(description="Title is a property in both Dataset and Distribution, in compliance with W3C DCAT. In some cases these might be identical, but in most cases the Dataset represents a more abstract concept, while the d", example="fast sun images")
    # Dataset ID
    dataset_id: DmpdatasetitemDatasetId = Field(description="Dataset ID")
    # Description is a property in both Dataset and Distribution, in compliance with W3C DCAT. In some ...
    description: Optional[str] = Field(description="Description is a property in both Dataset and Distribution, in compliance with W3C DCAT. In some cases these might be identical, but in most cases the Dataset represents a more abstract concept, while", example="mountains of data")
    subject: Optional[List[DmpdatasetitemSubjectItem]] = None
    keyword: Optional[List[str]] = None
    # If appropriate, type according to: DataCite and/or COAR dictionary. Otherwise use the common name...
    type_field: Optional[str] = Field(description="If appropriate, type according to: DataCite and/or COAR dictionary. Otherwise use the common name for the type, e.g. raw data, software, survey, etc. <a href=\"https://schema.datacite.org/meta/kernel-", example="image")
    # Main format of the dataset according to DDI General_DATA_Format controlled vocabulary  <a href="h...
    general_data_format: Optional[Enum23] = Field(description="Main format of the dataset according to DDI General_DATA_Format controlled vocabulary  <a href=\"https://rdf-vocabulary.ddialliance.org/ddi-cv/GeneralDataFormat/2.0.3/GeneralDataFormat.html.\">rdf-voc", example="stillimage")
    # If the dataset contains geospatial data, indicate the geodetic datum (coordinate reference system)
    geodetic_datum: Optional[Enum24] = Field(description="If the dataset contains geospatial data, indicate the geodetic datum (coordinate reference system)", example="ED50 (older European)")
    # The geographical area covered by a geospatial dataset
    geographic_coverage: Optional[str] = Field(description="The geographical area covered by a geospatial dataset")
    dataset_documentation: Optional[List[DmpdatasetitemDatasetDocumentationItem]] = None
    # Dataset percentage completeness
    data_completeness: Optional[Enum25] = Field(description="Dataset percentage completeness ", example="95% or greater")
    # Date the dataset was issued. Encoded using the relevant ISO 8601 Date <a href="https://www.w3.org...
    issued: Optional[date] = Field(description="Date the dataset was issued. Encoded using the relevant ISO 8601 Date <a href=\"https://www.w3.org/TR/NOTE-datetime\">compliant string</a>", example="2023-07-20")
    # Language of the dataset expressed using ISO 639-3
    language: Optional[Enum26] = Field(description="Language of the dataset expressed using ISO 639-3", example="mul")
    # To indicate how important the data are for the organization to achieve its goals. Example values:...
    data_criticality: Optional[str] = Field(description="To indicate how important the data are for the organization to achieve its goals. Example values: business function continuity and improvement, IM or IT modernization, intergovernmental agreement, min", example="business function continuity and improvement")
    # Indicate if there is a data governance framework like, for example, a specified departmental data...
    data_governance_description: str = Field(description="Indicate if there is a data governance framework like, for example, a specified departmental data governance framework, and/or if data governance is aligned with published principles such as the CARE ")
    # To indicate if personal data exists in the dataset
    personal_data: Enum27 = Field(description="To indicate if personal data exists in the dataset", example="yes")
    # Sensitive data are data for which injury that could reasonably be expected as a result of a loss ...
    sensitive_data: Enum28 = Field(description="Sensitive data are data for which injury that could reasonably be expected as a result of a loss of confidentiality (resulting from unauthorized disclosure), loss of integrity (resulting from unauthor", example="yes")
    security_and_privacy: Optional[List[DmpdatasetitemSecurityAndPrivacyItem]] = None
    # Preservation Statement. Details concerning retention and disposition should be provided in dmp/da...
    preservation_statement: Optional[str] = Field(description="Preservation Statement. Details concerning retention and disposition should be provided in dmp/dataset/disposition_planning/")
    supported_works_url: Optional[List[AnyUrl]] = None
    # Information on how the data is collected.
    collection: Optional[DmpdatasetitemCollection] = Field(description="Information on how the data is collected.")
    technical_resource: Optional[List[DmpdatasetitemTechnicalResourceItem]] = None
    data_quality_assurance: Optional[List[str]] = None
    # Intellectual property related to the dataset
    intellectual_property: DmpdatasetitemIntellectualProperty = Field(description="Intellectual property related to the dataset")
    # limitation affecting the fitness for use of the resource or metadata
    disclaimer: Optional[str] = Field(description="limitation affecting the fitness for use of the resource or metadata", example="not to be used for navigation")
    metadata: Optional[List[DmpdatasetitemMetadataItem]] = None
    distribution: Optional[List[DmpdatasetitemDistributionItem]] = None
    # To describe the operational environment used for data collection, processing, analysis and dissem...
    computing_environment: Optional[DmpdatasetitemComputingEnvironment] = Field(description="To describe the operational environment used for data collection, processing, analysis and dissemination.")
    # Used to document information in anticipation of a future disposition review.  Note: does not docu...
    disposition_planning: Optional[DmpdatasetitemDispositionPlanning] = Field(description="Used to document information in anticipation of a future disposition review.  Note: does not document disposition actions actually effected.")
    disposition_action: Optional[List[DmpdatasetitemDispositionActionItem]] = None
    # Date on which disposition effected. Encoded using the ISO 8601 Date <a href="https://www.w3.org/T...
    disposition_completed: Optional[date] = Field(description="Date on which disposition effected. Encoded using the ISO 8601 Date <a href=\"https://www.w3.org/TR/NOTE-datetime\">compliant string</a>", example="2019-06-0

")


    @validator('dataset_id')
    def validate_dataset_id(cls, v):
        """Validate dataset_id"""
        # Add custom validation logic here
        return v

class Dmpgeneralinfo(BaseModel):
    """
    Generated class for DmpGeneralInfo
    """

    # Title of the DMP
    title: str = Field(description="Title of the DMP", example="The most exciting project ever.")
    # To provide any free-form text information on the Data Management Plan (maDMP). It can be a formal...
    description: Optional[str] = Field(description="To provide any free-form text information on the Data Management Plan (maDMP). It can be a formal statement describing how research data will be managed and documented throughout a research project an", example="We're finally get our data under control.")
    # Identifier for the DMP itself
    dmp_id: DmpgeneralinfoDmpId = Field(description="Identifier for the DMP itself")
    # Date and time of the first version of the maDMP. This date must not be changed in subsequent maDM...
    created: datetime = Field(description="Date and time of the first version of the maDMP. This date must not be changed in subsequent maDMPs. Each maDMP has a \"Created\" date and a \"Modified\" date. The modification date contains a timesta", example="2021-04-15T10:53:49-5:00")
    # Indicates maDMP version, so must be set each time the maDMP is modified.  Dates can be used to in...
    modified: datetime = Field(description="Indicates maDMP version, so must be set each time the maDMP is modified.  Dates can be used to indicate past and planned actions. Dataset contains issue date that indicates whether the actions are pla", example="2021-04-22T14:32:54-5:00")
    # Language of the DMP expressed using ISO 639-3
    language: Enum1 = Field(description="Language of the DMP expressed using ISO 639-3", example="eng")
    # OPEN ACCESS: available to anyone based on an open license (e.g., CC0, CC-BY, Open Government Lice...
    access: Enum2 = Field(description="OPEN ACCESS: available to anyone based on an open license (e.g., CC0, CC-BY, Open Government License). SHARED ACCESS: Public access with a license that limits use, that is available to anyone under te", example="open")
    # Protected information is not classified. Information is "protected" when unauthorized disclosure ...
    protection_level: Enum3 = Field(description="Protected information is not classified. Information is \"protected\" when unauthorized disclosure could reasonably be expected to cause injury to a non-national interest (i.e., an individual interest", example="protected A")
    # To indicate the protection level of the dmp according to the nomenclature found in dmp_protection...
    protection_level_other_nomenclature: Optional[str] = Field(description="To indicate the protection level of the dmp according to the nomenclature found in dmp_protection_level_other_nomenclature")
    # To indicate which nomenclature is used to qualify the dmp protection level when "other" was chose...
    protection_level_other_level: Optional[str] = Field(description="To indicate which nomenclature is used to qualify the dmp protection level when \"other\" was chosen in dmp_protection_level")
    # Security classification that designates the level of protection against access the DMP requires w...
    security_classification_level: Enum4 = Field(description="Security classification that designates the level of protection against access the DMP requires when unauthorized disclosure could reasonably be expected to cause injury to the national interest – def", example="unclassified")
    # To indicate the level of the classified information held by the dmp according to the nomenclature...
    security_classification_level_other_nomenclature: Optional[str] = Field(description="To indicate the level of the classified information held by the dmp according to the nomenclature found in dmp_security_classification_level_other_nomenclature")
    # To indicate which nomenclature is used to qualify classified information
    security_classification_level_other_level: Optional[str] = Field(description="To indicate which nomenclature is used to qualify classified information")
    # The version you are using is v2.5, you should not edit this field.
    schema_version: str = Field(description="The version you are using is v2.5, you should not edit this field.")
    # DMP schema URI
    schema_version_uri: AnyUrl = Field(description="DMP schema URI ")
    # To indicate whether there are ethical issues related to these data. It is the responsibility of t...
    ethical_issues_exist: Enum5 = Field(description="To indicate whether there are ethical issues related to these data. It is the responsibility of the researcher or data steward to be aware of any ethical issues related to the data. Ethical issues var", example="yes")
    # To describe any existing or potential ethical issues that are not captured in the following relat...
    ethical_issues_description: Optional[str] = Field(description="To describe any existing or potential ethical issues that are not captured in the following related fields: dmp/protection_level, dmp/security_classification_level, dmp/dataset/disposition_planning/le", example="There are ethical issues because...")
    # To indicate, for example, where a protocol from a meeting with an ethical committee can be found,...
    ethical_issues_report: Optional[AnyUrl] = Field(description="To indicate, for example, where a protocol from a meeting with an ethical committee can be found, or an IRB (Institutional Review Board) report.", example="http://report.location")
    linked_dmp: Optional[List[DmpgeneralinfoLinkedDmpItem]] = None


    @validator('dmp_id')
    def validate_dmp_id(cls, v):
        """Validate dmp_id"""
        # Add custom validation logic here
        return v

class Dmpindigenousconsiderations(BaseModel):
    """
    Indigenous considerations related to the maDMP or the data.
    """

    # To indicate if Indigenous consideration exist.
    exist: Enum17 = Field(description="To indicate if Indigenous consideration exist.", example="yes")
    community_approval: Optional[List[DmpindigenousconsiderationsCommunityApprovalItem]] = None
    group_identification: Optional[List[str]] = None
    # Indigenous government group affiliated with the contributor
    government_name: Optional[str] = Field(description="Indigenous government group affiliated with the contributor")
    # To indicate if Indigenous language is used. If the answer is "yes," then it is identified under m...
    language: Optional[Enum18] = Field(description="To indicate if Indigenous language is used. If the answer is \"yes,\" then it is identified under maDMP language and/or Dataset language and/or Distribution language.", example="yes")
    # To indicate if Indigenous characters are used in the data. If the answer is "yes," then 'Unified ...
    characters: Optional[Enum19] = Field(description="To indicate if Indigenous characters are used in the data. If the answer is \"yes,\" then 'Unified Canadian Aboriginal Syllabics Extended' is identified under distribution/character_unicode_block", example="yes")
    research_method: Optional[List[str]] = None
    indian_band_name: Optional[List[Enum20]] = None
    indian_band_number: Optional[List[Enum21]] = None


    @root_validator
    def validate_conditional_0(cls, values):
        """Validate conditional schema 0"""
        # Add conditional validation logic here
        return values

    @root_validator
    def validate_conditional_1(cls, values):
        """Validate conditional schema 1"""
        # Add conditional validation logic here
        return values

    @root_validator
    def validate_conditional_2(cls, values):
        """Validate conditional schema 2"""
        # Add conditional validation logic here
        return values

    @root_validator
    def validate_conditional_3(cls, values):
        """Validate conditional schema 3"""
        # Add conditional validation logic here
        return values

    @root_validator
    def validate_conditional_4(cls, values):
        """Validate conditional schema 4"""
        # Add conditional validation logic here
        return values

class Dmpprojectitem(BaseModel):
    """
    Project related to the DMP
    """

    # Project title
    title: str = Field(description="Project title", example="Mission to the sun.")
    # Project description
    description: Optional[str] = Field(description="Project description", example="Important data for secret mission to the sun")
    # Project start date. Encoded using the relevant ISO 8601 Date <a href="https://www.w3.org/TR/NOTE-...
    start: Optional[date] = Field(description="Project start date. Encoded using the relevant ISO 8601 Date <a href=\"https://www.w3.org/TR/NOTE-datetime\">compliant string</a>", example="2023-07-20")
    # Project end date. Encoded using the relevant ISO 8601 Date <a href="https://www.w3.org/TR/NOTE-da...
    end: Optional[date] = Field(description="Project end date. Encoded using the relevant ISO 8601 Date <a href=\"https://www.w3.org/TR/NOTE-datetime\">compliant string</a>", example="2025-12-31")
    funding: Optional[List[DmpprojectitemFundingItem]]
    partner_organization: Optional[List[DmpprojectitemPartnerOrganizationItem]] = None
    # Science is defined broadly to include the natural, health, and social sciences, mathematics, engi...
    safeguarding_science_measures: DmpprojectitemSafeguardingScienceMeasures = Field(description="Science is defined broadly to include the natural, health, and social sciences, mathematics, engineering, and technology. Safeguarding science includes safeguarding research partnerships, opensource d")
    # Succession plan, or business continuity plan
    succession_plan: str = Field(description="Succession plan, or business continuity plan", example="No plan, project falls apart when P.I. leaves.")
    # To indicate if an algorithmic impact assessment has been conducted related to these data. For exa...
    algorithmic_impact_assessment_conducted: Optional[Enum16] = Field(description="To indicate if an algorithmic impact assessment has been conducted related to these data. For example using this <a href=\"https://www.canada.ca/en/government/system/digital-government/digital-governm", example="yes")
    # Link to the algorithmic impact assessment
    algorithmic_impact_assessment_conducted_uri: Optional[AnyUrl] = Field(description="Link to the algorithmic impact assessment")


    @validator('safeguarding_science_measures')
    def validate_safeguarding_science_measures(cls, v):
        """Validate safeguarding_science_measures"""
        # Add custom validation logic here
        return v

class Dmpcontactcontactid(BaseModel):
    """
    Persistent identifier associated with the contact
    """

    # Contact person's unique identifier
    identifier: str = Field(description="Contact person's unique identifier", example="0000-0000-0000-0000")
    # Identifier type. It is recommended to use ORCID ID for scientists and researchers.
    type_field: Enum76 = Field(description="Identifier type. It is recommended to use ORCID ID for scientists and researchers.", example="orcid")
    # Link to the identifier system used to identify the contact
    registry_uri: Optional[AnyUrl] = Field(description="Link to the identifier system used to identify the contact", example="https://gcdirectory-gcannuaire.ssc-spc.gc.ca/en/GCD/?pgid=002")
    # To indicate the version number or date when the system was consulted
    registry_version: Optional[str] = Field(description="To indicate the version number or date when the system was consulted", example="2024/12/30")


    @root_validator
    def validate_conditional_0(cls, values):
        """Validate conditional schema 0"""
        # Add conditional validation logic here
        return values

class Dmpcontactcountry(BaseModel):
    """
    Country of the organization the contact is affiliated to
    """

    # The 2-letter code of the country, as per the ISO standard 3166-1 Codes for the representation of ...
    code: Enum78 = Field(description="The 2-letter code of the country, as per the ISO standard 3166-1 Codes for the representation of names of countries and their subdivisions – Part 1: Country codes", example="US")
    # The name of the country, as per the ISO standard ISO 3166-1 Codes for the representation of names...
    name: str = Field(description="The name of the country, as per the ISO standard ISO 3166-1 Codes for the representation of names of countries and their subdivisions – Part 1: Country codes", example="United States of America")


class Dmpcontactprovincestate(BaseModel):
    """
    Province or state of the location. For Canadian Provinces and Territories use the Data reference standard on Canadian provinces and territories 

    """

    # The 3-letter code of the province or state, as per the ISO standard 3166-2 Codes for the represen...
    code: str = Field(description="The 3-letter code of the province or state, as per the ISO standard 3166-2 Codes for the representation of names of countries and their subdivisions – Part 2: Country subdivision code", example="QC")
    # The name of the province or state, as per the ISO standard ISO 3166-2  Codes for the representati...
    name: str = Field(description="The name of the province or state, as per the ISO standard ISO 3166-2  Codes for the representation of names of countries and their subdivisions – Part 2: Country subdivision code", example="Quebec")


class Dmpcontributoritemaffiliationitem(BaseModel):
    """
    Contributor affiliation status with an outside organization
    """

    # Type of organization the contributor is affiliated to.
    type_field: Enum8 = Field(description="Type of organization the contributor is affiliated to.", example="No affiliation")
    # Country where the organization the contributor is affiliated to, is located
    country: Optional[DmpcontributoritemaffiliationitemCountry] = Field(description="Country where the organization the contributor is affiliated to, is located")
    # Province or state of the organization the contributor is affiliated to.
    province_state: Optional[DmpcontributoritemaffiliationitemProvinceState] = Field(description="Province or state of the organization the contributor is affiliated to. ")


    @root_validator
    def validate_conditional_0(cls, values):
        """Validate conditional schema 0"""
        # Add conditional validation logic here
        return values

    @root_validator
    def validate_conditional_1(cls, values):
        """Validate conditional schema 1"""
        # Add conditional validation logic here
        return values

    @root_validator
    def validate_conditional_2(cls, values):
        """Validate conditional schema 2"""
        # Add conditional validation logic here
        return values

    @root_validator
    def validate_conditional_3(cls, values):
        """Validate conditional schema 3"""
        # Add conditional validation logic here
        return values

    @root_validator
    def validate_conditional_4(cls, values):
        """Validate conditional schema 4"""
        # Add conditional validation logic here
        return values

class Dmpcontributoritemcontributorid(BaseModel):
    """
    Identifier of the contributor
    """

    # Identifier type. It is recommended to use ORCID ID for scientists and researchers.
    type_field: Enum7 = Field(description="Identifier type. It is recommended to use ORCID ID for scientists and researchers.", example="other")
    # Link to the identifier system used to identify the contributor
    registry_uri: Optional[AnyUrl] = Field(description="Link to the identifier system used to identify the contributor", example="https://gcdirectory-gcannuaire.ssc-spc.gc.ca/en/GCD/?pgid=002")
    # To indicate the version number or date when the system was consulted
    registry_version: Optional[str] = Field(description="To indicate the version number or date when the system was consulted", example="2024/12/30")
    # Identifier for a contributor person
    identifier: str = Field(description="Identifier for a contributor person", example="1111-1111-1111-1111")


    @root_validator
    def validate_conditional_0(cls, values):
        """Validate conditional schema 0"""
        # Add conditional validation logic here
        return values

class Dmpcontributoritemaffiliationitemcountry(BaseModel):
    """
    Country where the organization the contributor is affiliated to, is located
    """

    # The name of the country, as per the ISO standard ISO 3166-1 Codes for the representation of names...
    country_name: str = Field(description="The name of the country, as per the ISO standard ISO 3166-1 Codes for the representation of names of countries and their subdivisions – Part 1: Country codes", example="United States of America")
    # The 2-letter code of the country, as per the ISO standard 3166-1 Codes for the representation of ...
    country_code: Enum9 = Field(description="The 2-letter code of the country, as per the ISO standard 3166-1 Codes for the representation of names of countries and their subdivisions – Part 1: Country codes", example="US")


class Dmpcontributoritemaffiliationitemprovincestate(BaseModel):
    """
    Province or state of the organization the contributor is affiliated to. 
    """

    # The name of the province or state, as per the ISO standard ISO 3166-2  Codes for the representati...
    name: str = Field(description="The name of the province or state, as per the ISO standard ISO 3166-2  Codes for the representation of names of countries and their subdivisions – Part 2: Country subdivision code", example="Quebec")
    # The 3-letter code of the province or state, as per the ISO standard 3166-2 Codes for the represen...
    code: str = Field(description="The 3-letter code of the province or state, as per the ISO standard 3166-2 Codes for the representation of names of countries and their subdivisions – Part 2: Country subdivision code", example="QC")


class Dmpcostitemcostdocumentationitem(BaseModel):
    """
    Any external material documenting the costing details.
    """

    # Title of the external document documenting the  cost
    name: str = Field(description="Title of the external document documenting the  cost", example="Project Business Charter")
    # A URL of that gives access to the costdocumentation. e.g., landing page, feed, SPARQL endpoint. T...
    access_url: Optional[AnyUrl] = Field(description="A URL of that gives access to the costdocumentation. e.g., landing page, feed, SPARQL endpoint. The access URL should be used for the URL of a service or location that can provide access to cost docum", example="https://007gc.sharepoint.com/stb/wqmsd/Project.aspx")
    # Download URL to the cost documentation
    download_url: Optional[AnyUrl] = Field(description="Download URL to the cost documentation", example="https://gc.sharepoint.com/dpmo/projects/project 345 business charter.docx")


class Dmpdatasetitemcollection(BaseModel):
    """
    Information on how the data is collected.
    """

    # To describe the data collection processes
    description: Optional[Enum30] = Field(description="To describe the data collection processes", example="7 days")
    # The earliest date in the case of time series data. Encoded using the ISO 8601 Date  <a href="http...
    data_earliest_date: Optional[date] = Field(description="The earliest date in the case of time series data. Encoded using the ISO 8601 Date  <a href=\"https://www.w3.org/TR/NOTE-datetime\">compliant string</a>", example="1973-01-01")
    # For time series data, date of the latest data in the series. Encoded using the relevant ISO 8601 ...
    data_latest_date: Optional[date] = Field(description="For time series data, date of the latest data in the series. Encoded using the relevant ISO 8601 Date <a href=\"https://www.w3.org/TR/NOTE-datetime\">compliant string</a>", example="2022-12-31")
    # The update frequency of the data in the dataset
    dataset_update_frequency: Optional[Enum31] = Field(description="The update frequency of the data in the dataset", example="7 days")
    # Date when the dataset was last updated. Encoded using the relevant ISO 8601 Date <a href="https:/...
    dataset_last_updated: Optional[date] = Field(description="Date when the dataset was last updated. Encoded using the relevant ISO 8601 Date <a href=\"https://www.w3.org/TR/NOTE-datetime\">compliant string</a>", example="2023-07-20")
    # Size of the dataset from which distributions are derived.
    dataset_size: Optional[float] = Field(description="Size of the dataset from which distributions are derived.", example="5")
    # Dataset size units
    dataset_size_units: Optional[Enum32] = Field(description="Dataset size units", example="PB - petabytes")
    # The dataset annual growth rate in terabytes per year. Important information needed when planning ...
    growth_annual_terabytes: Optional[float] = Field(description="The dataset annual growth rate in terabytes per year. Important information needed when planning storage and associated budget")
    # Date on which data collection for this dataset is expected to cease
    growth_end_date: Optional[date] = Field(description="Date on which data collection for this dataset is expected to cease")


class Dmpdatasetitemcomputingenvironment(BaseModel):
    """
    To describe the operational environment used for data collection, processing, analysis and dissemination.
    """

    # To describe the database management system or other computerized data system used to store or man...
    data_management_system: Optional[DmpdatasetitemcomputingenvironmentDataManagementSystem] = Field(description="To describe the database management system or other computerized data system used to store or manage active data. Every other systems used to access the data should be described as distributions.")
    # Minimum requirements for processing data for a specific purpose
    hardware_requirements: Optional[DmpdatasetitemcomputingenvironmentHardwareRequirements] = Field(description="Minimum requirements for processing data for a specific purpose
")
    software: Optional[List[DmpdatasetitemcomputingenvironmentSoftwareItem]] = None


    @validator('hardware_requirements')
    def validate_hardware_requirements(cls, v):
        """Validate hardware_requirements"""
        # Add custom validation logic here
        return v

class Dmpdatasetitemdatasetdocumentationitem(BaseModel):
    """
    Repeat as many times as needed to list all existing documentation, procedures for data processing, management, analysis and dissemination. For example: code book, contract, data dictionary, data production specification (ISO 19131 compliant), ELN (electronic lab notebook) protocol, QA/QC methods, SOP (standard operating procedure), workflows, etc. Make sure you also provide the related computing environment information in the dedicated section.
    """

    # Document name
    name: Optional[str] = Field(description="Document name", example="code book")
    # To describe the documentation of the dataset
    description: Optional[str] = Field(description="To describe the documentation of the dataset")
    # A URL of that gives access to the dataset documentation. e.g., landing page, feed, SPARQL endpoin...
    access_url: Optional[AnyUrl] = Field(description="A URL of that gives access to the dataset documentation. e.g., landing page, feed, SPARQL endpoint. The access URL should be used for the URL of a service or location that can provide access to datase", example="some.intranet/.../project page.aspx")
    # Download URL to the dataset documentation
    download_url: Optional[AnyUrl] = Field(description="Download URL to the dataset documentation", example="http://some.repo.../download/...")


class Dmpdatasetitemdatasetid(BaseModel):
    """
    Dataset ID
    """

    # Identifier for a dataset
    identifier: str = Field(description="Identifier for a dataset", example="University of Vienna Phaidra (univie.ac.at)")
    # Identifier type
    type_field: Enum22 = Field(description="Identifier type", example="url")
    # Link to the registry or system used to identify the dataset
    registry_uri: Optional[AnyUrl] = Field(description="Link to the registry or system used to identify the dataset", example="https://ecollab.ncr.int.ec.gc.ca/theme/1275886/DataInventoryProject/Data-Asset-Inventory_Master-Spreadsheet_FINAL.xlsx")
    # To indicate the version number or date when the system or registry was consulted
    registry_version: Optional[str] = Field(description="To indicate the version number or date when the system or registry was consulted", example="2024-12-30")


    @root_validator
    def validate_conditional_0(cls, values):
        """Validate conditional schema 0"""
        # Add conditional validation logic here
        return values

class Dmpdatasetitemdispositionactionitem(BaseModel):
    """
    Used to document disposition that has been effected in relation to a dataset. Note: when all data associated with a dataset has been disposed, also indicate this in “dmp/dataset/disposition_completed.” 
    """

    # Indicate the type of action taken to dispose of the data (scope of the data to be detailed in the...
    type_field: Enum75 = Field(description="Indicate the type of action taken to dispose of the data (scope of the data to be detailed in the description field)", example="alienated from government control")
    # To document the scope of disposition action effected, as well as other details when required (e.g...
    description: str = Field(description="To document the scope of disposition action effected, as well as other details when required (e.g., means, issues).", example="All non-disseminated datasets associated with the project NameMyTree were manually deleted from drive Z by project lead. Litigation and ATIP checks had been completed 2020-01-12. Backup tapes not addressed.")
    # Indicate disposition authorization number and details when available. May also be used to documen...
    authorization: str = Field(description="Indicate disposition authorization number and details when available. May also be used to document internal signoff.", example="DA 2018/001, Application Guide for National Registration version 2.0. Signoff by CIO on 2020-03-13.")
    # Date on which disposition effected. Encoded using the ISO 8601 Date <a href="https://www.w3.org/T...
    date: date = Field(description="Date on which disposition effected. Encoded using the ISO 8601 Date <a href=\"https://www.w3.org/TR/NOTE-datetime\">compliant string</a>", example="2020-03-1

")


class Dmpdatasetitemdispositionplanning(BaseModel):
    """
    Used to document information in anticipation of a future disposition review. 
Note: does not document disposition actions actually effected.
    """

    retention_specification: Optional[List[DmpdatasetitemdispositionplanningRetentionSpecificationItem]] = None
    # A URL that leads to the applicable retention schedule
    retention_schedule_url: Optional[AnyUrl] = Field(description="A URL that leads to the applicable retention schedule", example="//intranet.server/.../retention_schedule")
    # Archival value according to the national archives. (Applies to records only.)  For the Government...
    archival_value: Enum73 = Field(description="Archival value according to the national archives. (Applies to records only.)  For the Government of Canada, refer to definition of government records in the Library and Archives of Canada Act. Nevert", example="yes all")
    # Indicates nature and scope of the data with archival value according to national archives.
    archival_value_description: Optional[str] = Field(description="Indicates nature and scope of the data with archival value according to national archives.")
    # To record the most recent date on which the values entered in disposition planning properties wer...
    disposition_planning_last_reviewed: Optional[date] = Field(description="To record the most recent date on which the values entered in disposition planning properties were reviewed. Encoded using the relevant ISO 8601 Date <a href=\"https://www.w3.org/TR/NOTE-datetime\">co", example="2024-11-20")
    disposition_impediment: Optional[List[DmpdatasetitemdispositionplanningDispositionImpedimentItem]] = None
    disposition_review_next: Optional[List[date]] = None


class Dmpdatasetitemdistributionitem(BaseModel):
    """
    To provide technical information on a specific instance of data.
    """

    # Title is a property in both Dataset and Distribution, in compliance with W3C DCAT. In some cases ...
    title: str = Field(description="Title is a property in both Dataset and Distribution, in compliance with W3C DCAT. In some cases these might be identical, but in most cases the Dataset represents a more abstract concept, while the d", example="Full resolution images")
    # Description is a property in both Dataset and Distribution, in compliance with W3C DCAT. In some ...
    description: Optional[str] = Field(description="Description is a property in both Dataset and Distribution, in compliance with W3C DCAT. In some cases these might be identical, but in most cases the Dataset represents a more abstract concept, while", example="best quality data before resizing")
    # Date of creation of the distribution. Encoded using the relevant ISO 8601 Date <a href="https://w...
    created: Optional[date] = Field(description="Date of creation of the distribution. Encoded using the relevant ISO 8601 Date <a href=\"https://www.w3.org/TR/NOTE-datetime\">compliant string</a>", example="2009-11-02")
    # Date of the publication of the distribution. Encoded using the relevant ISO 8601 Date <a href="ht...
    issued: date = Field(description="Date of the publication of the distribution. Encoded using the relevant ISO 8601 Date <a href=\"https://www.w3.org/TR/NOTE-datetime\">compliant string</a>")
    # The maintenance and update frequency of the distribution
    update_frequency: Enum36 = Field(description="The maintenance and update frequency of the distribution", example="daily")
    # Completion status of the distribution
    status: Optional[Enum37] = Field(description="Completion status of the distribution", example="finished")
    # Description of the distribution completion status
    status_description: Optional[str] = Field(description="Description of the distribution completion status ")
    # Version history of the data distribution
    version_history: Optional[DmpdatasetitemdistributionitemVersionHistory] = Field(description="Version history of the data distribution")
    # Indicates how long this distribution will be/should be available. Encoded using the relevant ISO ...
    available_until: Optional[date] = Field(description="Indicates how long this distribution will be/should be available. Encoded using the relevant ISO 8601 Date <a href=\"https://www.w3.org/TR/NOTE-datetime\">compliant string</a>", example="2023-07-20")
    # Date of the oldest data in the distribution. Encoded using the relevant ISO 8601 Date <a href="ht...
    start_date: date = Field(description="Date of the oldest data in the distribution. Encoded using the relevant ISO 8601 Date <a href=\"https://www.w3.org/TR/NOTE-datetime\">compliant string</a>")
    # Date of the most recent data in the distribution. Encoded using the relevant ISO 8601 Date <a hre...
    end_date: Optional[date] = Field(description="Date of the most recent data in the distribution. Encoded using the relevant ISO 8601 Date <a href=\"https://www.w3.org/TR/NOTE-datetime\">compliant string</a>")
    # Rectangular spatial extent that encompasses all the geographic locations represented in the data
    geographic_bounding_box: Optional[DmpdatasetitemdistributionitemGeographicBoundingBox] = Field(description="Rectangular spatial extent that encompasses all the geographic locations represented in the data")
    # Spatial representation type of the data
    spatial_representation_type: Optional[Enum38] = Field(description="Spatial representation type of the data")
    # Character encoding standard used in the distribution
    character_encoding_standard: Enum39 = Field(description="Character encoding standard used in the distribution", example="UTF-16")
    # Unicode block used in the distribution. Use the "code" from the Unicode Standard 15.1. <a href="h...
    character_unicode_block_code: Enum40 = Field(description="Unicode block used in the distribution. Use the \"code\" from the Unicode Standard 15.1. <a href=\"https://en.wikipedia.org/wiki/Unicode_block#List_of_blocks\">en.wikipedia.org</a>", example="U+18B0..U+18FF")
    # Unicode block used in the distribution. Use the "name" from the Unicode Standard 15.1. <a href="h...
    character_unicode_block_name: Enum41 = Field(description="Unicode block used in the distribution. Use the \"name\" from the Unicode Standard 15.1. <a href=\"https://en.wikipedia.org/wiki/Unicode_block#List_of_blocks\">en.wikipedia.org</a>", example="Unified Canadian Aboriginal Syllabics Extended")
    # Data quality control level. LEVEL 0 (Raw data or minimally processed data. Contains all available...
    quality_control_level: Enum42 = Field(description="Data quality control level. LEVEL 0 (Raw data or minimally processed data. Contains all available measurement data. May contain quality control flags indicating missing or invalid data. LEVEL 1 (A com", example="level 0 - raw")
    # OPEN ACCESS: available to anyone based on an open license (e.g., CC0, CC-BY, Open Government Lice...
    data_access: Enum43 = Field(description="OPEN ACCESS: available to anyone based on an open license (e.g., CC0, CC-BY, Open Government License). SHARED ACCESS: Public access with a license that limits use, that is available to anyone under te", example="open")
    # To rate the level of openness of the dataset according to the Tim Berners-Lee (founder of the Wor...
    linked_data_star_rating: Enum44 = Field(description="To rate the level of openness of the dataset according to the Tim Berners-Lee (founder of the World Wide Web) 5-star rating system for open data. To score the maximum five stars, data must (1) be avai", example="1 star")
    # To rate the level of openness of the dataset according to a system other than the Tim Berner-Lees...
    openness_other_rating: Optional[str] = Field(description="To rate the level of openness of the dataset according to a system other than the Tim Berner-Lees system, described in dmp/dataset/openness_other_rating_system.")
    # Describe which system is used to rate the dataset openness when it's not the Tim Berners-Lee 5-st...
    openness_other_rating_system: str = Field(description="Describe which system is used to rate the dataset openness when it's not the Tim Berners-Lee 5-star rating system.")
    # Protected information is not classified. Information is "protected" when unauthorized disclosure ...
    protection_level: Enum45 = Field(description="Protected information is not classified. Information is \"protected\" when unauthorized disclosure could reasonably be expected to cause injury to a non-national interest (i.e., an individual interest", example="protected A")
    # Security classification that designates the level of protection against access the data or inform...
    security_classification_level: Enum46 = Field(description="Security classification that designates the level of protection against access the data or information requires when unauthorized disclosure could reasonably be expected to cause injury to the nationa", example="unclassified")
    data_security_privacy_measures: Optional[List[str]] = None
    preservation_flag: Optional[List[Enum47]] = None
    # Indicates the date on which the distribution was disposed. (That is, the data lifecycle is comple...
    disposition_completed: Optional[date] = Field(description="Indicates the date on which the distribution was disposed. (That is, the data lifecycle is complete, at the institution).  In instances in which data was never created in relation to a planned distrib", example="2015-03-24")
    # The host is the system where the data are stored and processed. Be sure to also fill in a physica...
    host: Optional[DmpdatasetitemdistributionitemHost] = Field(description="The host is the system where the data are stored and processed. Be sure to also fill in a physical data asset section to indicate in which the data are hosted especially in which country the server is")
    physical_data_asset: Optional[List[DmpdatasetitemdistributionitemPhysicalDataAssetItem]] = None
    format: Optional[List[str]]
    # ID for the distribution
    distribution_id: DmpdatasetitemdistributionitemDistributionId = Field(description="ID for the distribution")
    # File path is for files that are available internally on shared drives but are not published on th...
    file_path: Optional[AnyUrl] = Field(description="File path is for files that are available internally on shared drives but are not published on the internet. File paths using backslashes instead of forward slashes used for urls.")
    # A URL that gives access to the distribution. e.g., landing page, feed, SPARQL endpoint. The acces...
    access_url: Optional[AnyUrl] = Field(description="A URL that gives access to the distribution. e.g., landing page, feed, SPARQL endpoint. The access URL should be used for the URL of a service or location that can provide access to the distribution, ", example="some.data.catalogue/.../project_uuid")
    # The URL of the downloadable file in a given format. E.g. CSV file or RDF file.
    download_url: Optional[AnyUrl] = Field(description="The URL of the downloadable file in a given format. E.g. CSV file or RDF file.", example="http://some.repo.../download/...")
    online_service: Optional[List[DmpdatasetitemdistributionitemOnlineServiceItem]] = None
    # Size of the dataset distribution in measured in bytes.
    byte_size: Optional[float] = Field(description="Size of the dataset distribution in measured in bytes.", example="5,000,000,000,000")
    # The RDA standard requires describing size in bytes. However, byte_size does not provide a meaning...
    data_size: Optional[float] = Field(description="The RDA standard requires describing size in bytes. However, byte_size does not provide a meaningful number for large files (e.g., a file that would typically be expressed in petabytes). Also, other s", example="5")
    # The RDA standard requires describing size in bytes. However, byte_size does not provide a meaning...
    data_size_units: Optional[Enum57] = Field(description="The RDA standard requires describing size in bytes. However, byte_size does not provide a meaningful number for large files (e.g., a file that would typically be expressed in petabytes). Also, other s", example="PB - petabytes")


    @root_validator
    def validate_conditional_0(cls, values):
        """Validate conditional schema 0"""
        # Add conditional validation logic here
        return values

    @root_validator
    def validate_conditional_1(cls, values):
        """Validate conditional schema 1"""
        # Add conditional validation logic here
        return values

    @root_validator
    def validate_conditional_2(cls, values):
        """Validate conditional schema 2"""
        # Add conditional validation logic here
        return values

    @root_validator
    def validate_conditional_3(cls, values):
        """Validate conditional schema 3"""
        # Add conditional validation logic here
        return values

    @root_validator
    def validate_conditional_4(cls, values):
        """Validate conditional schema 4"""
        # Add conditional validation logic here
        return values

    @validator('distribution_id')
    def validate_distribution_id(cls, v):
        """Validate distribution_id"""
        # Add custom validation logic here
        return v

class Dmpdatasetitemintellectualproperty(BaseModel):
    """
    Intellectual property related to the dataset
    """

    # Description of the copyright associated with dataset
    copyright_description: Optional[str] = Field(description="Description of the copyright associated with dataset", example="Non-transferable copyright")
    # Limitations of the copyright associated with the dataset (e.g., all of the dataset; part of the d...
    copyright_extent: str = Field(description="Limitations of the copyright associated with the dataset (e.g., all of the dataset; part of the dataset; etc.)", example="Part of the dataset")
    copyright_holder: Optional[List[Enum33]] = Field(min_items=1)
    # To allow for the recording of other rights than copyright.
    other: Optional[str] = Field(description="To allow for the recording of other rights than copyright.")


    @validator('copyright_holder')
    def validate_copyright_holder(cls, v):
        """Validate copyright_holder"""
        # Add custom validation logic here
        return v

class Dmpdatasetitemmetadataitem(BaseModel):
    """
    To describe metadata standards used.
    """

    # Description
    description: Optional[str] = Field(description="Description", example="Provides taxonomy for...")
    # Language of the metadata expressed using ISO 639-3 (three letter language code)
    language: Enum34 = Field(description="Language of the metadata expressed using ISO 639-3 (three letter language code)", example="eng")
    # Metadata Standard ID
    metadata_standard_id: DmpdatasetitemmetadataitemMetadataStandardId = Field(description="Metadata Standard ID")


    @validator('metadata_standard_id')
    def validate_metadata_standard_id(cls, v):
        """Validate metadata_standard_id"""
        # Add custom validation logic here
        return v

class Dmpdatasetitemsecurityandprivacyitem(BaseModel):
    """
    To list all issues and requirements related to security and privacy. Create a new entry for each issue or requirement.
    """

    # Provide a title for each requirement or issue listed in the security and privacy section. Titles ...
    title: str = Field(description="Provide a title for each requirement or issue listed in the security and privacy section. Titles need to be specific enough to differentiate issues or requirements between them.", example="Physical access control; Special privacy requirement")
    # Description of security or privacy controls
    description: Optional[str] = Field(description="Description of security or privacy controls", example="Server with data must be kept in a locked room because ... ; Data cannot be shared even in de-identified or anonymised form.")
    # Privacy impact assessment related to the dataset. Government of Canada institutions should refer ...
    privacy_impact_assessment: DmpdatasetitemsecurityandprivacyitemPrivacyImpactAssessment = Field(description="Privacy impact assessment related to the dataset. Government of Canada institutions should refer to <a href=\"https://www.tbs-sct.canada.ca/pol/doc-eng.aspx?id=18309\">www.tbs-sct.canada.ca</a> ")


    @validator('privacy_impact_assessment')
    def validate_privacy_impact_assessment(cls, v):
        """Validate privacy_impact_assessment"""
        # Add custom validation logic here
        return v

class Dmpdatasetitemsubjectitem(BaseModel):
    """
    Topic to which a dataset pertains.
    """

    # Subject, classification code, or keyword describing the dataset. Privilege subject headings from ...
    heading_name: str = Field(description="Subject, classification code, or keyword describing the dataset. Privilege subject headings from controlled vocabularies (subject schemes).", example="Astronautics and state")
    # The URI of the subject heading value.
    heading_uri: Optional[AnyUrl] = Field(description="The URI of the subject heading value.", example="https://id.loc.gov/authorities/subjects/sh85008973.html")
    # To indicate the name of the subject scheme or classification system from which the subject headin...
    scheme_name: Optional[str] = Field(description="To indicate the name of the subject scheme or classification system from which the subject heading or classification code is drawn (e.g., Government of Canada Core Subject Thesaurus Government of Cana", example="X̱wi7x̱wa Classification Scheme")
    # The URI of the subject scheme. Consider URIs available at <a href="https://id.loc.gov/vocabulary/...
    scheme_uri: Optional[AnyUrl] = Field(description="The URI of the subject scheme. Consider URIs available at <a href=\"https://id.loc.gov/vocabulary/subjectSchemes.html\">id.loc.gov</a>", example="https://xwi7xwa-library-10nov2016.sites.olt.ubc.ca/files/2021/06/2018-MODIFIED-DEER-Public-version.pdf")
    # Specify the language and/or iteration number of the subject scheme that was used to assign the su...
    scheme_version: Optional[str] = Field(description="Specify the language and/or iteration number of the subject scheme that was used to assign the subject heading.", example="Updated 28 March 2018")


class Dmpdatasetitemtechnicalresourceitem(BaseModel):
    """
    To list technical resources involved in the collection and processing of the dataset, and for which documentation is required. For computing environments, please record information in the dmp/dataset/computing_environment field
    """

    # Name of the technical resource
    name: str = Field(description="Name of the technical resource", example="123/45/43/AT")
    # Description of the technical resource (e.g., aircraft, cell phones, database, datalogger, field a...
    description: Optional[str] = Field(description="Description of the technical resource (e.g., aircraft, cell phones, database, datalogger, field analyzer, laboratory instrument, modeling-simulation output, satellite, text document, survey, wildlife ")


class Dmpdatasetitemcomputingenvironmentdatamanagementsystem(BaseModel):
    """
    To describe the database management system or other computerized data system used to store or manage active data. Every other systems used to access the data should be described as distributions.
    """

    # Brief explanation of the system’s purpose and functionality in relation to this dataset. If relev...
    description: Optional[str] = Field(description="Brief explanation of the system’s purpose and functionality in relation to this dataset. If relevant, indicate the data types managed by the system.", example="During the active phase of the research project, active and transitory data are managed (versioned and tracked) within DBMSX.")
    pid_system: Optional[List[Enum58]] = None
    # Indicates whether checksum verification is supported
    support_checksum: Optional[Enum59] = Field(description="Indicates whether checksum verification is supported", example="yes")
    # To indicate if the data management system supports versioning, meaning changes to datasets are tr...
    support_versioning: Optional[Enum60] = Field(description="To indicate if the data management system supports versioning, meaning changes to datasets are tracked internally", example="yes")
    # Defines whether the system is operational 24/7 or has downtime periods (e.g., 24/7, scheduled dow...
    availability: Optional[str] = Field(description="Defines whether the system is operational 24/7 or has downtime periods (e.g., 24/7, scheduled downtime)", example="scheduled down time")
    # Backup frequency of the system
    backup_frequency: Optional[str] = Field(description="Backup frequency of the system", example="weekly")
    # Type of backup performed on the data (e.g., incremental, full, differential, synthetic full)
    backup_type: Optional[Enum61] = Field(description="Type of backup performed on the data (e.g., incremental, full, differential, synthetic full)")
    # Certifications relevant to the system (e.g., CoreTrustSeal, ISO 16363).
    certified_with: Optional[str] = Field(description="Certifications relevant to the system (e.g., CoreTrustSeal, ISO 16363).", example="coretrustseal")
    # The geographical location of the server hosting the system expressed using ISO 3166-1 country code.
    geo_location: Optional[Enum62] = Field(description="The geographical location of the server hosting the system expressed using ISO 3166-1 country code.", example="CA")
    # Defines whether the data management system is accessible internally within an organization or ext...
    visibility: Optional[Enum63] = Field(description="Defines whether the data management system is accessible internally within an organization or externally to broader audiences. Allowed values: Internal (accessible only within a specific organization ", example="Internal")
    # Version of the data management system
    version: Optional[str] = Field(description="Version of the data management system", example="3.2.1")
    # The type of system used to store and manage active data. Note that a time-series database like Ti...
    type_field: Enum64 = Field(description="The type of system used to store and manage active data. Note that a time-series database like TimescaleDB is actually a relational database, but because it's optimized for time-series data, it gets i", example="Relational Database Management System (RDBMS)")
    storage_type: Optional[List[Enum65]] = None
    # The protocol used to interact with the system
    protocol: Optional[Enum66] = Field(description="The protocol used to interact with the system ", example="OAI-PMH")
    # Maximum data transfer latency of the data en ms, i.e. the delay of data transfer
    data_transfer_maximum_latency: Optional[float] = Field(description="Maximum data transfer latency of the data en ms, i.e. the delay of data transfer ", example="50")
    compliance_standards: Optional[List[str]] = None
    authentication: Optional[List[Enum67]] = None
    # Web or network-accessible URL for accessing the system
    access_url: Optional[AnyUrl] = Field(description="Web or network-accessible URL for accessing the system", example="https://dmssystem.org")
    # Name of the active data management system used to store/process data (e.g., PostgreSQL, MySQL, Da...
    title: str = Field(description="Name of the active data management system used to store/process data (e.g., PostgreSQL, MySQL, Dataverse, iRODS, etc.)", example="NAQUA")
    # Maximum throughput of the dataset in Mbps
    data_transfer_minimum_throughput: Optional[float] = Field(description="Maximum throughput of the dataset in Mbps", example="10")


class Dmpdatasetitemcomputingenvironmenthardwarerequirements(BaseModel):
    """
    Minimum requirements for processing data for a specific purpose

    """

    # Minimum CPU cores needed to process the data
    minimum_cpu_cores: Optional[float] = Field(description="Minimum CPU cores needed to process the data", example="8")
    # Minimum clock speed needed to process the data in GHz
    minimum_cpu_speed: Optional[float] = Field(description="Minimum clock speed needed to process the data in GHz", example="2.5 GHz")
    # Minimum computing type needed to process the data (e.g., desktop workstation, laptop, server, HPC...
    machine_type: Optional[str] = Field(description="Minimum computing type needed to process the data (e.g., desktop workstation, laptop, server, HPC, super computer)", example="HPC")
    # Minimum RAM needed to process the data in GB
    data_processing_minimum_ram_gb: Optional[float] = Field(description="Minimum RAM needed to process the data in GB", example="16")
    # Minimum bandwidth or network speed required in Mbps
    network_requirements: Optional[float] = Field(description="Minimum bandwidth or network speed required in Mbps", example="100")
    # Need for specialized hardware like GPUs/TPUs for computation
    processing_unit_requirements: Optional[str] = Field(description="Need for specialized hardware like GPUs/TPUs for computation", example="NVIDIA Tesla V100")
    # Minimum storage space required
    storage_capacity: Optional[float] = Field(description="Minimum storage space required", example="1")
    # Describe the hardware requirement
    description: str = Field(description="Describe the hardware requirement")
    # Identify the purpose requiring specific hardware
    title: str = Field(description="Identify the purpose requiring specific hardware", example="Mapping historical data")


    @root_validator
    def validate_conditional_0(cls, values):
        """Validate conditional schema 0"""
        # Add conditional validation logic here
        return values

class Dmpdatasetitemcomputingenvironmentsoftwareitem(BaseModel):
    """
    Repeat as many times as needed to describe all software and code used for data collection, data processing, data analysis, data dissemination. Fill in the computer code or the software section. If applicable, fill also the proprietary software section and the software management plan section. You may also want to provide information about the workflow documentation in the dedicated section.
    """

    # To provide any free-form text information on the computer code used
    description: Optional[str] = Field(description="To provide any free-form text information on the computer code used", example="Tool for spatial data analysis")
    # the URL to download the computer code or the software
    download_url: Optional[AnyUrl] = Field(description="the URL to download the computer code or the software")
    # The licensing terms under which the software/code is released. Use of a license is highly recomme...
    license: Optional[str] = Field(description="The licensing terms under which the software/code is released. Use of a license is highly recommended (e.g., CC BY-SA 4.0, MIT, open government license, etc.)", example="CC BY-SA 4.0")
    # Computer programming language used (e.g., Python, R, SAS)
    programming_language: Optional[str] = Field(description="Computer programming language used (e.g., Python, R, SAS)", example="Python")
    # To indicate if proprietary software was used
    proprietary_software: Enum68 = Field(description="To indicate if proprietary software was used", example="yes")
    # If OS dependent, operating system used
    operating_system_name: Optional[Enum69] = Field(description="If OS dependent, operating system used ", example="linux")
    # If OS dependent, to provide any free-form text information on the operating system used
    operating_system_description: Optional[str] = Field(description="If OS dependent, to provide any free-form text information on the operating system used")
    software_management_plan: Optional[List[DmpdatasetitemcomputingenvironmentsoftwareitemSoftwareManagementPlanItem]] = None
    # Link to a version control repository if applicable
    code_repository: Optional[AnyUrl] = Field(description="Link to a version control repository if applicable", example="https://github.com/FAIRERdata/maDMP-Standard")
    # Specify where the software runs (e.g., Local Machine, Cloud, Docker, Kubernetes, etc.)
    execution_environment: Optional[str] = Field(description="Specify where the software runs (e.g., Local Machine, Cloud, Docker, Kubernetes, etc.)", example="laptop")
    dependencies: Optional[List[str]] = None


    @root_validator
    def validate_conditional_0(cls, values):
        """Validate conditional schema 0"""
        # Add conditional validation logic here
        return values

    @root_validator
    def validate_conditional_1(cls, values):
        """Validate conditional schema 1"""
        # Add conditional validation logic here
        return values

    @root_validator
    def validate_conditional_2(cls, values):
        """Validate conditional schema 2"""
        # Add conditional validation logic here
        return values

    @root_validator
    def validate_conditional_3(cls, values):
        """Validate conditional schema 3"""
        # Add conditional validation logic here
        return values

class Dmpdatasetitemcomputingenvironmentsoftwareitemsoftwaremanagementplanitem(BaseModel):
    """
    To reference an external software management plan
    """

    name: str
    # A URL of that gives access to the Software Management Plan (SMP), e.g., landing page, feed, SPARQ...
    access_url: Optional[AnyUrl] = Field(description="A URL of that gives access to the Software Management Plan (SMP), e.g., landing page, feed, SPARQL endpoint. The access URL should be used for the URL of a service or location that can provide access ", example="some.intranet/.../project page.aspx")
    # Title of the external document documenting the  cost
    download_url: Optional[AnyUrl] = Field(description="Title of the external document documenting the  cost", example="http://some.repo.../download/...")


class Dmpdatasetitemdispositionplanningdispositionimpedimentitem(BaseModel):
    """
    For noting time-limited impediments to effecting disposition at the end of the dataset's retention period. Note: use the retention specification nest to indicate an indefinite retention period or a perpetual use requirement.
    """

    # To indicate the type of impediment to disposition.
    type_field: Enum74 = Field(description="To indicate the type of impediment to disposition.", example="agreement")
    # Include the name of the litigation, software, etc. where applicable. Note: if there is a known en...
    description: Optional[str] = Field(description="Include the name of the litigation, software, etc. where applicable. Note: if there is a known end date to the disposition impediment, consider entering it in “dmp/dataset/disposition_planning/disposi", example="Thompson et al v. Canada")


class Dmpdatasetitemdispositionplanningretentionspecificationitem(BaseModel):
    """
    Generated class for DmpdatasetitemdispositionplanningRetentionSpecificationItem
    """

    # Indicates how long a dataset should be retained, and why. In instances of determinate retention, ...
    description: str = Field(description="Indicates how long a dataset should be retained, and why. In instances of determinate retention, a retention specification includes three elements: a retention period; a retention period initiator (tr", example="See the Directive on the Management of Microdata for all retention specifications.")
    trigger_type: Optional[List[Enum70]] = None
    trigger_description: Optional[List[str]] = None
    retention_rationale: Optional[List[str]] = None
    # To record duration of retention period after trigger event. (At the end of its retention period, ...
    retention_period_duration: Optional[str] = Field(description="To record duration of retention period after trigger event. (At the end of its retention period, the dataset should be reviewed for disposition, and disposition effected if possible.) Encoded using th", example="P7Y2M0DT0H0M0S")
    retention_period_end_date: Optional[List[date]] = None
    # Examples where this may be required include the need to maintain scientific integrity, treaties, ...
    required_perpetual_use: Enum71 = Field(description="Examples where this may be required include the need to maintain scientific integrity, treaties, agreements, or compliance with UN Joinet-Orentlicher principles for the protection and promotion of hum", example="yes all")
    # To indicate that a dataset must be destroyed. Note: context can be recorded in “retention_specifi...
    required_destruction: Enum72 = Field(description="To indicate that a dataset must be destroyed. Note: context can be recorded in “retention_specifications/description.”", example="yes all")
    # The date on which the trigger was triggered, and the retention period began. Encoded using the re...
    trigger_occured: Optional[date] = Field(description="The date on which the trigger was triggered, and the retention period began. Encoded using the relevant ISO 8601 Date <a href=\"https://www.w3.org/TR/NOTE-datetime\">compliant string</a>.", example="2024-11-20")


    @root_validator
    def validate_conditional_0(cls, values):
        """Validate conditional schema 0"""
        # Add conditional validation logic here
        return values

class Dmpdatasetitemdistributionitemdistributionid(BaseModel):
    """
    ID for the distribution
    """

    # Identifier type
    type_field: str = Field(description="Identifier type", example="url")
    # Link to the reference used to identify the distribution
    registry_uri: Optional[AnyUrl] = Field(description="Link to the reference used to identify the distribution", example="https://data-donnees.az.ec.gc.ca/data")
    # To indicate the version number or date of the reference
    registry_version: Optional[str] = Field(description="To indicate the version number or date of the reference", example="2024/12/30")
    # Identifier for the dataset distribution
    identifier: str = Field(description="Identifier for the dataset distribution")


    @root_validator
    def validate_conditional_0(cls, values):
        """Validate conditional schema 0"""
        # Add conditional validation logic here
        return values

class Dmpdatasetitemdistributionitemgeographicboundingbox(BaseModel):
    """
    Rectangular spatial extent that encompasses all the geographic locations represented in the data
    """

    # The northernmost point of the area covered by the dataset, expressed in degrees of latitude
    north: str = Field(description="The northernmost point of the area covered by the dataset, expressed in degrees of latitude")
    # The easternmost point of the area covered by the dataset, expressed in degrees of latitude
    east: str = Field(description="The easternmost point of the area covered by the dataset, expressed in degrees of latitude")
    # The southernmost point of the area covered by the dataset, expressed in degrees of latitude
    south: str = Field(description="The southernmost point of the area covered by the dataset, expressed in degrees of latitude")
    # The westernmost point of the area covered by the dataset, expressed in degrees of latitude
    west: str = Field(description="The westernmost point of the area covered by the dataset, expressed in degrees of latitude")


class Dmpdatasetitemdistributionitemhost(BaseModel):
    """
    The host is the system where the data are stored and processed. Be sure to also fill in a physical data asset section to indicate in which the data are hosted especially in which country the server is located if they are hosted on a server or in the cloud.
    """

    # The name of the system hosting the dataset for external access
    title: str = Field(description="The name of the system hosting the dataset for external access", example="Super Repository")
    # To describe the host of the distribution
    description: Optional[str] = Field(description="To describe the host of the distribution", example="Repository hosted by...")
    # The URL of the system hosting a distribution of a dataset
    url: AnyUrl = Field(description="The URL of the system hosting a distribution of a dataset", example="https://zenodo.org")
    # The type of storage
    storage_type: Optional[Enum48] = Field(description="The type of storage ", example="cloud")
    pid_system: Optional[List[Enum49]] = None
    # To indicate if checksums can be performed to ensure integrity of the distribution
    support_checksum: Optional[Enum50] = Field(description="To indicate if checksums can be performed to ensure integrity of the distribution", example="yes")
    # To indicate if changes made in a version is supported from the host
    support_versioning: Optional[Enum51] = Field(description="To indicate if changes made in a version is supported from the host", example="yes")
    # Indicate if the data are active data, longterm storage, or transitory. For batch priority-based s...
    data_priority: Optional[str] = Field(description="Indicate if the data are active data, longterm storage, or transitory. For batch priority-based scheduling, declare relative priorities to determine the processing order of jobs and business processes", example="Longterm storage; batch processing priority low")
    # Defines whether the data host is operational 24/7 or has downtime periods
    availability: Optional[str] = Field(description="Defines whether the data host is operational 24/7 or has downtime periods", example="scheduled downtimes")
    # Backup Frequency
    backup_frequency: Optional[str] = Field(description="Backup Frequency", example="weekly")
    # Type of backup of the host (e.g., incremental, full, differential, synthetic full)
    backup_type: Optional[str] = Field(description="Type of backup of the host (e.g., incremental, full, differential, synthetic full)")
    # Repository certified to a recognised standard (e.g., ISO 27001, CoreTrustSeal, etc.)
    certified_with: Optional[str] = Field(description="Repository certified to a recognised standard (e.g., ISO 27001, CoreTrustSeal, etc.)", example="coretrustseal")
    # Maximum data transfer latency of the distribution en ms, i.e. the delay of data transfer
    data_transfer_maximum_latency: Optional[float] = Field(description="Maximum data transfer latency of the distribution en ms, i.e. the delay of data transfer ", example="50")
    # Maximum throughput of the distribution in Mbps
    data_transfer_minimum_throughput: Optional[float] = Field(description="Maximum throughput of the distribution in Mbps", example="10")
    # Physical location of the data expressed using ISO 3166-1 country code.
    geo_location: Optional[Enum52] = Field(description="Physical location of the data expressed using ISO 3166-1 country code.", example="CA")
    # The protocol used to interact with the host
    protocol: Optional[Enum53] = Field(description="The protocol used to interact with the host")
    # Content type of the online service (e.g., Web Service, Dataset, API, Application, Supporting Docu...
    content_type: Optional[str] = Field(description="Content type of the online service (e.g., Web Service, Dataset, API, Application, Supporting Document)", example="Web Service")
    # Format of the online service
    format: Optional[Enum54] = Field(description="Format of the online service", example="WMS")
    language: Optional[List[str]] = Field(min_items=1)
    # Version number of the host system, if applicable. This field is relevant for software-based hosts...
    version: Optional[str] = Field(description="Version number of the host system, if applicable. This field is relevant for software-based hosts such as repositories (e.g., Zenodo 4.0, Dataverse 5.13) or storage systems (e.g., MinIO RELEASE.2024-0")
    # The type of system hosting the distribution
    type_field: Enum55 = Field(description="The type of system hosting the distribution", example="data portal")


    @validator('language')
    def validate_language(cls, v):
        """Validate language"""
        # Add custom validation logic here
        return v

class Dmpdatasetitemdistributionitemonlineserviceitem(BaseModel):
    """
    To list online services where the distribution can be accessed
    """

    # Protocol of the online service
    protocol: Optional[Enum56] = Field(description="Protocol of the online service", example="OGC")
    # Language used for the online service
    service_language: Optional[str] = Field(description="Language used for the online service")
    # A url that leads to the online service page
    service_url: Optional[AnyUrl] = Field(description="A url that leads to the online service page")
    # Name of the online service
    name: Optional[str] = Field(description="Name of the online service", example="Emerald Ash Borer Regulated Areas")
    # Content type of the online service
    content_type: Optional[str] = Field(description="Content type of the online service", example="Web Service")
    # Format of the online service
    format: Optional[str] = Field(description="Format of the online service", example="WMS")
    language: Optional[List[str]] = Field(min_items=1)


    @validator('language')
    def validate_language(cls, v):
        """Validate language"""
        # Add custom validation logic here
        return v

class Dmpdatasetitemdistributionitemphysicaldataassetitem(BaseModel):
    """
    Allows for recording of physical assets (e.g., external hard drives) and/or physical location of servers, data centers, etc.
    """

    # Description of the physical data asset's appearance, functionality, contents, notable features, etc.
    description: str = Field(description="Description of the physical data asset's appearance, functionality, contents, notable features, etc.")
    # Physical object type the data is stored on (e.g., blue-ray, clay tablet, compact disk, dvd, hard ...
    type_field: str = Field(description="Physical object type the data is stored on (e.g., blue-ray, clay tablet, compact disk, dvd, hard drive, solid state drive, paper, parchment, stone, quipu, tape, thumb drive)", example="clay tablet")
    # City name where the physical asset(s) are located
    building_city: Optional[str] = Field(description="City name where the physical asset(s) are located")
    # Building name where the physical asset(s) are located
    building_name: Optional[str] = Field(description="Building name where the physical asset(s) are located")
    # Room number where the physical asset(s) are located
    building_room_number: Optional[str] = Field(description="Room number where the physical asset(s) are located")
    # Full device name in System where the physical asset(s) are located
    name: Optional[str] = Field(description="Full device name in System where the physical asset(s) are located")


class Dmpdatasetitemdistributionitemversionhistory(BaseModel):
    """
    Version history of the data distribution
    """

    # To indicate the date a revision of the data in the distribution was made. Encoded using the relev...
    revision_date: date = Field(description="To indicate the date a revision of the data in the distribution was made. Encoded using the relevant ISO 8601 Date <a href=\"https://www.w3.org/TR/NOTE-datetime\">compliant string</a>")
    # Description about what changes this revision made
    revision_description: Optional[str] = Field(description="Description about what changes this revision made")
    # Link to the revision documentation
    revision_documentation: Optional[AnyUrl] = Field(description="Link to the revision documentation")


class Dmpdatasetitemmetadataitemmetadatastandardid(BaseModel):
    """
    Metadata Standard ID
    """

    # Identifier type
    type_field: Enum35 = Field(description="Identifier type", example="url")
    # Link to the reference used to identify the metadata standard
    registry_uri: Optional[AnyUrl] = Field(description="Link to the reference used to identify the metadata standard ", example="https://rdamsc.bath.ac.uk/")
    # To indicate the version number or date of the reference
    registry_version: Optional[str] = Field(description="To indicate the version number or date of the reference", example="2024/10/10")
    # Identifier for the metadata standard used. Example: http://www.dublincore.org/specifications/dubl...
    identifier: str = Field(description="Identifier for the metadata standard used. Example: http://www.dublincore.org/specifications/dublin-core/dcmi-terms/", example="http://www.dublincore.org/specifications/dublin-core/dcmi-terms/")


    @root_validator
    def validate_conditional_0(cls, values):
        """Validate conditional schema 0"""
        # Add conditional validation logic here
        return values

class Dmpdatasetitemsecurityandprivacyitemprivacyimpactassessment(BaseModel):
    """
    Privacy impact assessment related to the dataset. Government of Canada institutions should refer to <a href="https://www.tbs-sct.canada.ca/pol/doc-eng.aspx?id=18309">www.tbs-sct.canada.ca</a> 
    """

    # A URL that gives access to the privacy impact assessment. The access URL should be used for the U...
    download_url: Optional[AnyUrl] = Field(description="A URL that gives access to the privacy impact assessment. The access URL should be used for the URL of a service or location that can provide access to privacy impact assessment, typically through a W", example="http://some.repo.../download/...")
    # To indicate if a privacy impact assessment is required.
    required: Enum29 = Field(description="To indicate if a privacy impact assessment is required.", example="yes")


    @root_validator
    def validate_conditional_0(cls, values):
        """Validate conditional schema 0"""
        # Add conditional validation logic here
        return values

    @root_validator
    def validate_conditional_1(cls, values):
        """Validate conditional schema 1"""
        # Add conditional validation logic here
        return values

    @root_validator
    def validate_conditional_2(cls, values):
        """Validate conditional schema 2"""
        # Add conditional validation logic here
        return values

class Dmpgeneralinfodmpid(BaseModel):
    """
    Identifier for the DMP itself
    """

    # Identifier type  (e.g., DOI)
    type_field: Enum0 = Field(description="Identifier type  (e.g., DOI)", example="doi")
    # Link to the registry or system used to identify the linked dmp
    registry_uri: Optional[AnyUrl] = Field(description="Link to the registry or system used to identify the linked dmp")
    # To indicate the version number or date of the reference
    registry_version: Optional[str] = Field(description="To indicate the version number or date of the reference")
    # Identifier for a DMP (e.g., DOI url)
    identifier: str = Field(description="Identifier for a DMP (e.g., DOI url)", example="https://doi.org/10.1371/journal.pcbi.1006750")


    @root_validator
    def validate_conditional_0(cls, values):
        """Validate conditional schema 0"""
        # Add conditional validation logic here
        return values

class Dmpgeneralinfolinkeddmpitem(BaseModel):
    """
    to link related dmps , for example official-language-equivalent dmps
    """

    linked_dmp_id: Optional[List[DmpgeneralinfolinkeddmpitemLinkedDmpIdItem]] = Field(min_items=1)
    # Explain the relationship of the linked dmp with respect to this dmp. Specify which version is aut...
    relationship: str = Field(description="Explain the relationship of the linked dmp with respect to this dmp. Specify which version is authoritative, if applicable. Example: the linked dmp is the working French version of this authoritative ", example="The linked dmp is the French version of this dmp")
    # URL where the linked dmp can be accessed
    access_url: Optional[AnyUrl] = Field(description="URL where the linked dmp can be accessed")
    # URL where the linked dmp can be downloaded
    download_url: Optional[AnyUrl] = Field(description="URL where the linked dmp can be downloaded", example="http://some.repo.../download/...")


    @validator('linked_dmp_id')
    def validate_linked_dmp_id(cls, v):
        """Validate linked_dmp_id"""
        # Add custom validation logic here
        return v

class Dmpgeneralinfolinkeddmpitemlinkeddmpiditem(BaseModel):
    """
    identifier of the related dmp
    """

    # type of identifier
    type_field: Enum6 = Field(description="type of identifier", example="doi")
    # Link to the reference used to identify the linked dmp
    registry_uri: Optional[AnyUrl] = Field(description="Link to the reference used to identify the linked dmp")
    # To indicate the version number or date of the reference
    registry_version: Optional[str] = Field(description="To indicate the version number or date of the reference")
    # identifier of the related dmp
    identifier: str = Field(description="identifier of the related dmp", example="https://doi.org/10.1371/journal.pcbi.1006750")


    @root_validator
    def validate_conditional_0(cls, values):
        """Validate conditional schema 0"""
        # Add conditional validation logic here
        return values

class Dmpindigenousconsiderationscommunityapprovalitem(BaseModel):
    """
    to describe approvals by indigenous communities
    """

    # To provide any free-form text information on the project approval from the Indigenous communities.
    status: Optional[str] = Field(description="To provide any free-form text information on the project approval from the Indigenous communities.")


    @root_validator
    def validate_conditional_0(cls, values):
        """Validate conditional schema 0"""
        # Add conditional validation logic here
        return values

class Dmpprojectitemfundingitem(BaseModel):
    """
    Funding related with a project
    """

    source: Optional[List[DmpprojectitemfundingitemSourceItem]] = Field(min_items=1)
    # Funder ID of the associated project
    funder_id: DmpprojectitemfundingitemFunderId = Field(description="Funder ID of the associated project")
    # To express different phases of project lifecycle.
    funding_status: Enum12 = Field(description="To express different phases of project lifecycle.", example="planned")
    # Grant ID of the associated project
    grant_id: Optional[DmpprojectitemfundingitemGrantId] = Field(description="Grant ID of the associated project")


    @validator('source')
    def validate_source(cls, v):
        """Validate source"""
        # Add custom validation logic here
        return v

    @validator('funder_id')
    def validate_funder_id(cls, v):
        """Validate funder_id"""
        # Add custom validation logic here
        return v

    @validator('grant_id')
    def validate_grant_id(cls, v):
        """Validate grant_id"""
        # Add custom validation logic here
        return v

class Dmpprojectitempartnerorganizationitem(BaseModel):
    """
    Partner organization
    """

    # Name of partner organization.
    name: Optional[str] = Field(description="Name of partner organization.", example="Stargazers")
    # Partner organization agreement
    agreement: Optional[DmpprojectitempartnerorganizationitemAgreement] = Field(description="Partner organization agreement")
    # Unique identifier assigned to represent partner organization.
    partner_organization_id: Optional[DmpprojectitempartnerorganizationitemPartnerOrganizationId] = Field(description="Unique identifier assigned to represent partner organization.")


    @validator('partner_organization_id')
    def validate_partner_organization_id(cls, v):
        """Validate partner_organization_id"""
        # Add custom validation logic here
        return v

class Dmpprojectitemsafeguardingsciencemeasures(BaseModel):
    """
    Science is defined broadly to include the natural, health, and social sciences, mathematics, engineering, and technology. Safeguarding science includes safeguarding research partnerships, opensource due diligence, and risk mitigation. Some elements of safeguarding science are recorded elsewhere in the maDMP (e.g., checksum, data access, data security-privacy measures, ethical issues, intellectual property, partner agreement, protection level, retention/disposition planning, security classification, succession plan, versioning). This section should be used for additional information (e.g., description and link to a risk assessment and mitigation plan). For guidance, see: <a href="https://www.oecd.org/content/dam/oecd/en/publications/reports/2022/06/integrity-and-security-in-the-global-research-ecosystem_2bd8511d/1c416f43-en.pdf">www.oecd.org</a>  ;  <a href="https://science.gc.ca/site/science/en/safeguarding-your-research/guidelines-and-tools-implement-research-security/guidance-conducting-open-source-due-diligence/conducting-open-source-due-diligence-safeguarding-research-partnerships">science.gc.ca</a>  ;  <a href="https://science.gc.ca/site/science/en/safeguarding-your-research/guidelines-and-tools-implement-research-security/mitigating-your-research-security-risks">science.gc.ca</a>  ;  <a href="https://science.gc.ca/site/science/en/safeguarding-your-research/guidelines-and-tools-implement-research-security/national-security-guidelines-research-partnerships/national-security-guidelines-research-partnerships-risk-assessment-form">science.gc.ca</a>
    """

    # To indicate if any measure has have been taken to safeguard the project
    exist: Enum14 = Field(description="To indicate if any measure has have been taken to safeguard the project", example="yes")
    # To indicate if any redundant backup of the source data exists
    redundant_backups_exist: Enum15 = Field(description="To indicate if any redundant backup of the source data exists")


    @root_validator
    def validate_conditional_0(cls, values):
        """Validate conditional schema 0"""
        # Add conditional validation logic here
        return values

    @root_validator
    def validate_conditional_1(cls, values):
        """Validate conditional schema 1"""
        # Add conditional validation logic here
        return values

class Dmpprojectitemfundingitemfunderid(BaseModel):
    """
    Funder ID of the associated project
    """

    # Identifier type
    type_field: Enum11 = Field(description="Identifier type", example="fundref")
    # Link to the registry or system used to identify the funder
    registry_uri: Optional[AnyUrl] = Field(description="Link to the registry or system used to identify the funder")
    # To indicate the version number or date when the registry or reference was consulted
    registry_version: Optional[str] = Field(description="To indicate the version number or date when the registry or reference was consulted")
    # Funder ID, recommended to use CrossRef Funder Registry. See: <a href="https://www.crossref.org/se...
    identifier: str = Field(description="Funder ID, recommended to use CrossRef Funder Registry. See: <a href=\"https://www.crossref.org/services/funder-registry/\">www.crossref.org</a>")


    @root_validator
    def validate_conditional_0(cls, values):
        """Validate conditional schema 0"""
        # Add conditional validation logic here
        return values

class Dmpprojectitemfundingitemgrantid(BaseModel):
    """
    Grant ID of the associated project
    """

    # Identifier type
    type_field: Optional[Enum13] = Field(description="Identifier type", example="url")
    # Link to the reference used to identify the grant
    registry_uri: Optional[AnyUrl] = Field(description="Link to the reference used to identify the grant")
    # To indicate the version number or date of the reference
    registry_version: Optional[str] = Field(description="To indicate the version number or date of the reference")
    # Grant ID
    identifier: str = Field(description="Grant ID", example="12345-987654")


    @root_validator
    def validate_conditional_0(cls, values):
        """Validate conditional schema 0"""
        # Add conditional validation logic here
        return values

class Dmpprojectitemfundingitemsourceitem(BaseModel):
    """
    Project funding source
    """

    type_field: Optional[List[str]] = Field(min_items=1)
    # To provide any free-form text information on the funding source for the project
    description: Optional[str] = Field(description="To provide any free-form text information on the funding source for the project")


    @validator('type_field')
    def validate_type_field(cls, v):
        """Validate type_field"""
        # Add custom validation logic here
        return v

class Dmpprojectitempartnerorganizationitemagreement(BaseModel):
    """
    Partner organization agreement
    """

    # To provide any free-form text information description on the agreement made with the partner orga...
    description: Optional[str] = Field(description="To provide any free-form text information description on the agreement made with the partner organization", example="Agrees to gaze at the stars")
    # Partner organization agreement type (e.g., MOA - Memorandum of Agreement; MOU - Memorandum of Und...
    type_field: str = Field(description="Partner organization agreement type (e.g., MOA - Memorandum of Agreement; MOU - Memorandum of Understanding; Indigenous data sharing agreement; BCR-band council resolution; Treaty; collaborative agree", example="cooperative agreement")
    # Download link to the partner organization agreement document
    agreement_download_url: Optional[AnyUrl] = Field(description="Download link to the partner organization agreement document ", example="http://some.repo.../download/...")


class Dmpprojectitempartnerorganizationitempartnerorganizationid(BaseModel):
    """
    Unique identifier assigned to represent partner organization.
    """

    # Partner organization identifier type
    type_field: Optional[str] = Field(description="Partner organization identifier type")
    # Link to the reference used to identify the partner organization
    registry_uri: Optional[AnyUrl] = Field(description="Link to the reference used to identify the partner organization")
    # To indicate the version number or date of the reference
    registry_version: Optional[str] = Field(description="To indicate the version number or date of the reference")
    # Partner organization identifier
    identifier: Optional[str] = Field(description="Partner organization identifier")


    @root_validator
    def validate_conditional_0(cls, values):
        """Validate conditional schema 0"""
        # Add conditional validation logic here
        return values

