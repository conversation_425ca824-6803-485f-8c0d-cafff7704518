{% extends "base.html" %}

{% block title %}DMP Examples{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2 class="mb-4">
            <i class="fas fa-eye text-success"></i>
            Example Data Management Plans
        </h2>
        
        <p class="lead">
            Explore these example DMPs to understand the structure and content of a well-formed Data Management Plan 
            following the RDA-DMP Common Standard.
        </p>

        {% if examples %}
            <div class="row">
                {% for example in examples %}
                <div class="col-lg-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">{{ example.title }}</h5>
                            <small class="text-muted">{{ example.filename }}</small>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <strong>Contact:</strong> 
                                {{ example.data.dmp.contact.name if example.data.dmp.contact else 'Not specified' }}
                            </div>
                            <div class="mb-3">
                                <strong>Language:</strong> 
                                {{ example.data.dmp.language if example.data.dmp.language else 'Not specified' }}
                            </div>
                            <div class="mb-3">
                                <strong>Datasets:</strong> 
                                {{ example.data.dmp.dataset|length if example.data.dmp.dataset else 0 }} dataset(s)
                            </div>
                            <div class="mb-3">
                                <strong>Ethical Issues:</strong> 
                                {{ example.data.dmp.ethical_issues_exist if example.data.dmp.ethical_issues_exist else 'Not specified' }}
                            </div>
                        </div>
                        <div class="card-footer">
                            <button class="btn btn-primary btn-sm" onclick="viewExample('{{ example.filename }}')">
                                <i class="fas fa-eye"></i> View Details
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="downloadExample('{{ example.filename }}')">
                                <i class="fas fa-download"></i> Download
                            </button>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                No example files found. Make sure the data directory contains example JSON files.
            </div>
        {% endif %}
    </div>
</div>

<!-- Modal for viewing example details -->
<div class="modal fade" id="exampleModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalTitle">DMP Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <pre><code id="exampleContent"></code></pre>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="copyToClipboard()">
                    <i class="fas fa-copy"></i> Copy JSON
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
const examples = {{ examples | tojson }};

function viewExample(filename) {
    const example = examples.find(ex => ex.filename === filename);
    if (example) {
        document.getElementById('exampleModalTitle').textContent = example.title;
        document.getElementById('exampleContent').textContent = JSON.stringify(example.data, null, 2);
        new bootstrap.Modal(document.getElementById('exampleModal')).show();
    }
}

function downloadExample(filename) {
    const example = examples.find(ex => ex.filename === filename);
    if (example) {
        const dataStr = JSON.stringify(example.data, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.click();
        URL.revokeObjectURL(url);
    }
}

function copyToClipboard() {
    const content = document.getElementById('exampleContent').textContent;
    navigator.clipboard.writeText(content).then(() => {
        // Show temporary success message
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check"></i> Copied!';
        btn.classList.remove('btn-primary');
        btn.classList.add('btn-success');
        
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-primary');
        }, 2000);
    });
}
</script>
{% endblock %}
