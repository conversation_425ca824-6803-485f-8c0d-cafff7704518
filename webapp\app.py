"""
Flask Web Application for DMP Generation using madmpy
"""
import os
import sys
import json
import datetime
from flask import Flask, render_template, request, jsonify, send_file, flash, redirect, url_for
from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SelectField, RadioField, FieldList, FormField
from wtforms.validators import DataRequired, Email, URL, Optional
from io import BytesIO

# Add the src directory to Python path to import madmpy
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))
import madmpy

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-change-in-production'

# Initialize madmpy
dmp_module = madmpy.load()

class ContactForm(FlaskForm):
    name = StringField('Contact Name', validators=[DataRequired()])
    email = StringField('Email', validators=[DataRequired(), Email()])
    orcid = StringField('ORCID ID', validators=[Optional(), URL()])

class DatasetForm(FlaskForm):
    title = StringField('Dataset Title', validators=[DataRequired()])
    description = TextAreaField('Dataset Description', validators=[DataRequired()])
    personal_data = RadioField('Contains Personal Data?', 
                              choices=[('yes', 'Yes'), ('no', 'No'), ('unknown', 'Unknown')],
                              default='no', validators=[DataRequired()])
    sensitive_data = RadioField('Contains Sensitive Data?', 
                               choices=[('yes', 'Yes'), ('no', 'No'), ('unknown', 'Unknown')],
                               default='no', validators=[DataRequired()])
    dataset_id = StringField('Dataset Identifier (DOI/URL)', validators=[Optional(), URL()])
    technical_resource_name = StringField('Technical Resource Name', validators=[DataRequired()])

class DMPForm(FlaskForm):
    title = StringField('DMP Title', validators=[DataRequired()])
    description = TextAreaField('DMP Description', validators=[Optional()])
    language = SelectField('Language', 
                          choices=[('eng', 'English'), ('spa', 'Spanish'), ('fra', 'French'), ('deu', 'German')],
                          default='eng', validators=[DataRequired()])
    dmp_id = StringField('DMP Identifier (DOI/URL)', validators=[Optional(), URL()])
    ethical_issues_exist = RadioField('Do Ethical Issues Exist?',
                                     choices=[('yes', 'Yes'), ('no', 'No'), ('unknown', 'Unknown')],
                                     default='no', validators=[DataRequired()])
    ethical_issues_description = TextAreaField('Ethical Issues Description', validators=[Optional()])

@app.route('/')
def index():
    """Main page with DMP creation form"""
    return render_template('index.html')

@app.route('/create_dmp', methods=['GET', 'POST'])
def create_dmp():
    """Handle DMP creation form"""
    if request.method == 'POST':
        try:
            # Get form data
            form_data = request.get_json() if request.is_json else request.form.to_dict()
            
            # Create DMP components
            contact = dmp_module.Contact(
                name=form_data['contact_name'],
                contact_id=dmp_module.ContactIdentifier(
                    identifier=form_data.get('contact_orcid', 'https://orcid.org/0000-0000-0000-0000'),
                    type=dmp_module.contact_id_type.ORCID,
                ),
                mbox=form_data['contact_email'],
            )

            dataset = dmp_module.Dataset(
                dataset_id=dmp_module.DatasetIdentifier(
                    identifier=form_data.get('dataset_id', 'https://doi.org/10.25504/FAIRsharing.r3vtvx'),
                    type=dmp_module.dmp_dataset_id_type.DOI,
                ),
                description=form_data['dataset_description'],
                personal_data=form_data['personal_data'],
                sensitive_data=form_data['sensitive_data'],
                technical_resource=[dmp_module.TechnicalResource(name=form_data['technical_resource_name'])],
                title=form_data['dataset_title'],
            )

            dmp_id = dmp_module.DMPIdentifier(
                identifier=form_data.get('dmp_id', 'https://doi.org/10.15497/rda00039'),
                type=dmp_module.dmp_dataset_id_type.DOI
            )

            # Create DMP object
            dmp = dmp_module.DMP(
                dataset=[dataset],
                language=getattr(dmp_module.LanguageEnum, form_data['language']),
                title=form_data['dmp_title'],
                contact=contact,
                dmp_id=dmp_id,
                ethical_issues_exist=getattr(dmp_module.YesNoUnknown, form_data['ethical_issues_exist'].upper()),
                created=datetime.datetime.now().replace(microsecond=0),
                modified=datetime.datetime.now().replace(microsecond=0),
                description=form_data.get('dmp_description'),
                ethical_issues_description=form_data.get('ethical_issues_description'),
            )

            # Export to JSON
            dmp_json = madmpy.export_DMP_json(dmp)
            
            if request.is_json:
                return jsonify({
                    'success': True,
                    'dmp': dmp_json,
                    'message': 'DMP created successfully!'
                })
            else:
                flash('DMP created successfully!', 'success')
                return render_template('result.html', dmp_json=json.dumps(dmp_json, indent=2))
                
        except Exception as e:
            error_msg = f'Error creating DMP: {str(e)}'
            if request.is_json:
                return jsonify({'success': False, 'error': error_msg}), 400
            else:
                flash(error_msg, 'error')
                return redirect(url_for('create_dmp'))
    
    return render_template('create_dmp.html')

@app.route('/download_dmp', methods=['POST'])
def download_dmp():
    """Download DMP as JSON file"""
    try:
        dmp_data = request.get_json()
        
        # Create a file-like object
        json_str = json.dumps(dmp_data, indent=2)
        file_obj = BytesIO(json_str.encode('utf-8'))
        
        return send_file(
            file_obj,
            as_attachment=True,
            download_name='dmp.json',
            mimetype='application/json'
        )
    except Exception as e:
        return jsonify({'error': str(e)}), 400

@app.route('/validate_dmp', methods=['POST'])
def validate_dmp():
    """Validate an uploaded DMP file"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file uploaded'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        # Read and validate the file
        file_content = file.read().decode('utf-8')
        dmp_data = json.loads(file_content)
        
        # Validate using madmpy
        dmp_instance = dmp_module.DMP(**dmp_data["dmp"])
        dmp_module.DMP.model_validate(dmp_instance)
        
        return jsonify({
            'success': True,
            'message': 'DMP is valid!',
            'dmp': dmp_data
        })
        
    except json.JSONDecodeError:
        return jsonify({'error': 'Invalid JSON format'}), 400
    except Exception as e:
        return jsonify({'error': f'Validation failed: {str(e)}'}), 400

@app.route('/examples')
def examples():
    """Show example DMPs"""
    examples_dir = os.path.join(os.path.dirname(__file__), '..', 'data')
    examples = []
    
    if os.path.exists(examples_dir):
        for filename in os.listdir(examples_dir):
            if filename.endswith('.json'):
                filepath = os.path.join(examples_dir, filename)
                try:
                    with open(filepath, 'r') as f:
                        data = json.load(f)
                        examples.append({
                            'filename': filename,
                            'title': data.get('dmp', {}).get('title', 'Untitled'),
                            'data': data
                        })
                except:
                    continue
    
    return render_template('examples.html', examples=examples)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
