#!/usr/bin/env python3
"""
Example usage of the generated gcwg_madmp library

Generated on: 2025-06-13 10:32:05
Source schema: data\GCWG-RDA-maDMP-schema.json
"""

import gcwg_madmp

def main():
    """Example usage of the library"""
    
    # Example 1: Load from JSON file
    try:
        # Replace 'data.json' with your actual data file
        # data = gcwg_madmp.load_from_file('data.json')
        # print("✅ Successfully loaded data from file")
        pass
    except FileNotFoundError:
        print("⚠️  No data file found - create one to test loading")
    except Exception as e:
        print(f"❌ Error loading from file: {e}")
    
    # Example 2: Create new instance
    try:
        # Create a new DMP instance (adjust fields as needed)
        # dmp = gcwg_madmp.DMP(
        #     # Add required fields here based on your schema
        # )
        # print("✅ Successfully created new DMP instance")
        pass
    except Exception as e:
        print(f"❌ Error creating instance: {e}")
    
    # Example 3: Validate JSON string
    sample_json = '{"test": "data"}'
    is_valid = gcwg_madmp.validate_json(sample_json)
    print(f"📝 JSON validation result: {is_valid}")
    
    # Example 4: Export to JSON
    try:
        # json_output = gcwg_madmp.export_to_json(dmp)
        # print("✅ Successfully exported to JSON")
        pass
    except Exception as e:
        print(f"❌ Error exporting to JSON: {e}")

if __name__ == "__main__":
    main()
