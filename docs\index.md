# Welcome to madmpy

**madmpy** is a Python library for creating and validating Data Management Plans (DMPs) following the recommendation of the  [RDA-DMP Common Standard Working Group](https://www.rd-alliance.org/groups/dmp-common-standards-wg/outputs/?output=94576) .

DMPs are commonly used in research institutions, data management workflows, and academic projects to facilitate the structured documentation of datasets, compliance with funding requirements, and integration with research data repositories. The library is derived from the [RDA-DMP Common Standard](https://github.com/RDA-DMP-Common/RDA-DMP-Common-Standard) specification, providing a structured approach to defining DMPs. 

## Getting Started

To begin using `madmpy`, check out the [Installation](quickstart.md#installation) guide to see how to create and validate your first DMP.

- Ready to code? → [Quickstart](quickstart.md)
- Found a bug or want to contact? → [<EMAIL>](mailto:<EMAIL>?subject=madmpy), [<EMAIL>](mailto:<EMAIL>?subject=madmpy)
- Read the source? → [Github](https://github.com/msicilia/madmpy)
