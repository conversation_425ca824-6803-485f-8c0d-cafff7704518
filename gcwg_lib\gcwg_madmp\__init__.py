"""
gcwg_madmp - Advanced Generated Library from JSON Schema

Generated on: 2025-06-13 10:32:05
Source schema: data\GCWG-RDA-maDMP-schema.json
Features: Advanced validation, conditional schemas, custom validators
"""

from .models import (
    DMP,
    DmpApproval,
    DmpContact,
    DmpContributorItem,
    DmpCostItem,
    DmpDatasetItem,
    DmpGeneralInfo,
    DmpIndigenousConsiderations,
    DmpProjectItem,
    DmpcontactContactId,
    DmpcontactCountry,
    DmpcontactProvinceState,
    DmpcontributoritemAffiliationItem,
    DmpcontributoritemContributorId,
    DmpcontributoritemaffiliationitemCountry,
    DmpcontributoritemaffiliationitemProvinceState,
    DmpcostitemCostDocumentationItem,
    DmpdatasetitemCollection,
    DmpdatasetitemComputingEnvironment,
    DmpdatasetitemDatasetDocumentationItem,
    DmpdatasetitemDatasetId,
    DmpdatasetitemDispositionActionItem,
    DmpdatasetitemDispositionPlanning,
    DmpdatasetitemDistributionItem,
    DmpdatasetitemIntellectualProperty,
    DmpdatasetitemMetadataItem,
    DmpdatasetitemSecurityAndPrivacyItem,
    DmpdatasetitemSubjectItem,
    DmpdatasetitemTechnicalResourceItem,
    DmpdatasetitemcomputingenvironmentDataManagementSystem,
    DmpdatasetitemcomputingenvironmentHardwareRequirements,
    DmpdatasetitemcomputingenvironmentSoftwareItem,
    DmpdatasetitemcomputingenvironmentsoftwareitemSoftwareManagementPlanItem,
    DmpdatasetitemdispositionplanningDispositionImpedimentItem,
    DmpdatasetitemdispositionplanningRetentionSpecificationItem,
    DmpdatasetitemdistributionitemDistributionId,
    DmpdatasetitemdistributionitemGeographicBoundingBox,
    DmpdatasetitemdistributionitemHost,
    DmpdatasetitemdistributionitemOnlineServiceItem,
    DmpdatasetitemdistributionitemPhysicalDataAssetItem,
    DmpdatasetitemdistributionitemVersionHistory,
    DmpdatasetitemmetadataitemMetadataStandardId,
    DmpdatasetitemsecurityandprivacyitemPrivacyImpactAssessment,
    DmpgeneralinfoDmpId,
    DmpgeneralinfoLinkedDmpItem,
    DmpgeneralinfolinkeddmpitemLinkedDmpIdItem,
    DmpindigenousconsiderationsCommunityApprovalItem,
    DmpprojectitemFundingItem,
    DmpprojectitemPartnerOrganizationItem,
    DmpprojectitemSafeguardingScienceMeasures,
    DmpprojectitemfundingitemFunderId,
    DmpprojectitemfundingitemGrantId,
    DmpprojectitemfundingitemSourceItem,
    DmpprojectitempartnerorganizationitemAgreement,
    DmpprojectitempartnerorganizationitemPartnerOrganizationId,
)

from .utils import (
    load_from_json,
    load_from_file,
    export_to_json,
    export_to_file,
    validate_json,
)

from .enums import (
    Enum0,
    Enum1,
    Enum10,
    Enum11,
    Enum12,
    Enum13,
    Enum14,
    Enum15,
    Enum16,
    Enum17,
    Enum18,
    Enum19,
    Enum2,
    Enum20,
    Enum21,
    Enum22,
    Enum23,
    Enum24,
    Enum25,
    Enum26,
    Enum27,
    Enum28,
    Enum29,
    Enum3,
    Enum30,
    Enum31,
    Enum32,
    Enum33,
    Enum34,
    Enum35,
    Enum36,
    Enum37,
    Enum38,
    Enum39,
    Enum4,
    Enum40,
    Enum41,
    Enum42,
    Enum43,
    Enum44,
    Enum45,
    Enum46,
    Enum47,
    Enum48,
    Enum49,
    Enum5,
    Enum50,
    Enum51,
    Enum52,
    Enum53,
    Enum54,
    Enum55,
    Enum56,
    Enum57,
    Enum58,
    Enum59,
    Enum6,
    Enum60,
    Enum61,
    Enum62,
    Enum63,
    Enum64,
    Enum65,
    Enum66,
    Enum67,
    Enum68,
    Enum69,
    Enum7,
    Enum70,
    Enum71,
    Enum72,
    Enum73,
    Enum74,
    Enum75,
    Enum76,
    Enum77,
    Enum78,
    Enum8,
    Enum9,
)

__version__ = "1.0.0"
__all__ = ['DmpgeneralinfoDmpId', 'DmpgeneralinfolinkeddmpitemLinkedDmpIdItem', 'DmpgeneralinfoLinkedDmpItem', 'DmpGeneralInfo', 'DmpcontributoritemContributorId', 'DmpcontributoritemaffiliationitemCountry', 'DmpcontributoritemaffiliationitemProvinceState', 'DmpcontributoritemAffiliationItem', 'DmpContributorItem', 'DmpApproval', 'DmpprojectitemfundingitemSourceItem', 'DmpprojectitemfundingitemFunderId', 'DmpprojectitemfundingitemGrantId', 'DmpprojectitemFundingItem', 'DmpprojectitempartnerorganizationitemAgreement', 'DmpprojectitempartnerorganizationitemPartnerOrganizationId', 'DmpprojectitemPartnerOrganizationItem', 'DmpprojectitemSafeguardingScienceMeasures', 'DmpProjectItem', 'DmpcostitemCostDocumentationItem', 'DmpCostItem', 'DmpindigenousconsiderationsCommunityApprovalItem', 'DmpIndigenousConsiderations', 'DmpdatasetitemDatasetId', 'DmpdatasetitemSubjectItem', 'DmpdatasetitemDatasetDocumentationItem', 'DmpdatasetitemsecurityandprivacyitemPrivacyImpactAssessment', 'DmpdatasetitemSecurityAndPrivacyItem', 'DmpdatasetitemCollection', 'DmpdatasetitemTechnicalResourceItem', 'DmpdatasetitemIntellectualProperty', 'DmpdatasetitemmetadataitemMetadataStandardId', 'DmpdatasetitemMetadataItem', 'DmpdatasetitemdistributionitemVersionHistory', 'DmpdatasetitemdistributionitemGeographicBoundingBox', 'DmpdatasetitemdistributionitemHost', 'DmpdatasetitemdistributionitemPhysicalDataAssetItem', 'DmpdatasetitemdistributionitemDistributionId', 'DmpdatasetitemdistributionitemOnlineServiceItem', 'DmpdatasetitemDistributionItem', 'DmpdatasetitemcomputingenvironmentDataManagementSystem', 'DmpdatasetitemcomputingenvironmentHardwareRequirements', 'DmpdatasetitemcomputingenvironmentsoftwareitemSoftwareManagementPlanItem', 'DmpdatasetitemcomputingenvironmentSoftwareItem', 'DmpdatasetitemComputingEnvironment', 'DmpdatasetitemdispositionplanningRetentionSpecificationItem', 'DmpdatasetitemdispositionplanningDispositionImpedimentItem', 'DmpdatasetitemDispositionPlanning', 'DmpdatasetitemDispositionActionItem', 'DmpDatasetItem', 'DmpcontactContactId', 'DmpcontactProvinceState', 'DmpcontactCountry', 'DmpContact', 'DMP', 'Enum0', 'Enum1', 'Enum2', 'Enum3', 'Enum4', 'Enum5', 'Enum6', 'Enum7', 'Enum8', 'Enum9', 'Enum10', 'Enum11', 'Enum12', 'Enum13', 'Enum14', 'Enum15', 'Enum16', 'Enum17', 'Enum18', 'Enum19', 'Enum20', 'Enum21', 'Enum22', 'Enum23', 'Enum24', 'Enum25', 'Enum26', 'Enum27', 'Enum28', 'Enum29', 'Enum30', 'Enum31', 'Enum32', 'Enum33', 'Enum34', 'Enum35', 'Enum36', 'Enum37', 'Enum38', 'Enum39', 'Enum40', 'Enum41', 'Enum42', 'Enum43', 'Enum44', 'Enum45', 'Enum46', 'Enum47', 'Enum48', 'Enum49', 'Enum50', 'Enum51', 'Enum52', 'Enum53', 'Enum54', 'Enum55', 'Enum56', 'Enum57', 'Enum58', 'Enum59', 'Enum60', 'Enum61', 'Enum62', 'Enum63', 'Enum64', 'Enum65', 'Enum66', 'Enum67', 'Enum68', 'Enum69', 'Enum70', 'Enum71', 'Enum72', 'Enum73', 'Enum74', 'Enum75', 'Enum76', 'Enum77', 'Enum78', 'load_from_json', 'load_from_file', 'export_to_json', 'export_to_file', 'validate_json']
