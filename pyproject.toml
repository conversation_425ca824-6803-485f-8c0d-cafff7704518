[project]
name = "madmpy"
version = "0.1.2"
description = "Creating and validating DMPs following the RDA Common Standard"
license = "MIT"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "pydantic>=2.10.4",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project.urls]
Documentation = "https://madmpy.readthedocs.io/en/latest/"
Repository = "https://github.com/msicilia/madmpy"
Issues = "https://github.com/msicilia/madmpy/issues"