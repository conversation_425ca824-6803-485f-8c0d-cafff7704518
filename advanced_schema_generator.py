#!/usr/bin/env python3
"""
Advanced JSON Schema to Python Library Generator

Enhanced version that handles:
- Complex nested structures
- Conditional schemas (if/then/else, allOf, oneOf, anyOf)
- Pattern properties
- Dependencies
- Custom validators
- Reference resolution ($ref)

Usage:
    python advanced_schema_generator.py <schema_file> <output_directory> [library_name]
"""

import json
import os
import sys
import re
from pathlib import Path
from typing import Dict, List, Any, Optional, Set, Union
from dataclasses import dataclass, field
from datetime import datetime
from urllib.parse import urlparse


@dataclass
class ValidationRule:
    """Represents a validation rule for a field"""
    rule_type: str  # 'pattern', 'min_length', 'max_length', 'minimum', 'maximum', etc.
    value: Any
    message: Optional[str] = None


@dataclass
class ConditionalSchema:
    """Represents conditional schema logic (if/then/else)"""
    condition: Dict[str, Any]
    then_schema: Optional[Dict[str, Any]] = None
    else_schema: Optional[Dict[str, Any]] = None


@dataclass
class AdvancedFieldInfo:
    """Enhanced field information with validation and conditional logic"""
    name: str
    python_name: str
    type_hint: str
    default_value: str
    description: str
    is_required: bool
    is_array: bool
    enum_values: Optional[List[str]] = None
    format_type: Optional[str] = None
    validation_rules: List[ValidationRule] = field(default_factory=list)
    conditional_schemas: List[ConditionalSchema] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    examples: List[Any] = field(default_factory=list)


@dataclass
class AdvancedClassInfo:
    """Enhanced class information"""
    name: str
    python_name: str
    description: str
    fields: List[AdvancedFieldInfo]
    required_fields: Set[str]
    parent_path: str
    conditional_schemas: List[ConditionalSchema] = field(default_factory=list)
    additional_properties: bool = True
    pattern_properties: Dict[str, Dict] = field(default_factory=dict)


class AdvancedSchemaGenerator:
    """Advanced schema generator with enhanced capabilities"""
    
    def __init__(self, schema_file: str, output_dir: str, library_name: str = "generated_lib"):
        self.schema_file = schema_file
        self.output_dir = Path(output_dir)
        self.library_name = library_name
        self.schema = {}
        self.classes = {}
        self.enums = {}
        self.imports = set()
        self.references = {}  # Store resolved $ref schemas
        self.type_mappings = {}  # Custom type mappings
        
        # Enhanced reserved keywords
        self.reserved_keywords = {
            'class', 'def', 'if', 'else', 'elif', 'while', 'for', 'try', 'except',
            'finally', 'with', 'as', 'import', 'from', 'return', 'yield', 'lambda',
            'global', 'nonlocal', 'assert', 'del', 'pass', 'break', 'continue',
            'and', 'or', 'not', 'in', 'is', 'True', 'False', 'None', 'type',
            'model', 'field', 'validator', 'root_validator', 'config'
        }
    
    def load_schema(self):
        """Load and parse the JSON schema with reference resolution"""
        with open(self.schema_file, 'r', encoding='utf-8') as f:
            self.schema = json.load(f)
        
        # Resolve all $ref references
        self.resolve_references()
        print(f"Loaded schema from {self.schema_file}")
    
    def resolve_references(self):
        """Resolve $ref references in the schema"""
        def resolve_ref(obj, root_schema):
            if isinstance(obj, dict):
                if "$ref" in obj:
                    ref_path = obj["$ref"]
                    if ref_path.startswith("#/"):
                        # Internal reference
                        path_parts = ref_path[2:].split("/")
                        resolved = root_schema
                        for part in path_parts:
                            resolved = resolved[part]
                        return resolved
                    else:
                        # External reference (not implemented)
                        return obj
                else:
                    return {k: resolve_ref(v, root_schema) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [resolve_ref(item, root_schema) for item in obj]
            else:
                return obj
        
        self.schema = resolve_ref(self.schema, self.schema)
    
    def sanitize_name(self, name: str) -> str:
        """Enhanced name sanitization"""
        # Handle special cases
        if not name:
            return "unknown_field"
        
        # Convert various naming conventions to snake_case
        name = re.sub(r'([a-z0-9])([A-Z])', r'\1_\2', name)
        name = re.sub(r'[^a-zA-Z0-9_]', '_', name)
        name = re.sub(r'_+', '_', name).strip('_').lower()
        
        # Handle reserved keywords
        if name in self.reserved_keywords:
            name = f"{name}_field"
        
        # Ensure valid identifier
        if name and name[0].isdigit():
            name = f"field_{name}"
        
        return name or "unknown_field"
    
    def sanitize_class_name(self, name: str) -> str:
        """Enhanced class name sanitization"""
        if not name:
            return "UnknownClass"
        
        # Convert to PascalCase
        name = re.sub(r'[^a-zA-Z0-9_]', '_', name)
        parts = [part.capitalize() for part in name.split('_') if part]
        class_name = ''.join(parts)
        
        # Ensure valid identifier
        if class_name and class_name[0].isdigit():
            class_name = f"Class{class_name}"
        
        return class_name or "UnknownClass"
    
    def extract_validation_rules(self, field_schema: Dict) -> List[ValidationRule]:
        """Extract validation rules from field schema"""
        rules = []
        
        # String validations
        if "pattern" in field_schema:
            rules.append(ValidationRule("pattern", field_schema["pattern"]))
        if "minLength" in field_schema:
            rules.append(ValidationRule("min_length", field_schema["minLength"]))
        if "maxLength" in field_schema:
            rules.append(ValidationRule("max_length", field_schema["maxLength"]))
        
        # Numeric validations
        if "minimum" in field_schema:
            rules.append(ValidationRule("ge", field_schema["minimum"]))
        if "maximum" in field_schema:
            rules.append(ValidationRule("le", field_schema["maximum"]))
        if "exclusiveMinimum" in field_schema:
            rules.append(ValidationRule("gt", field_schema["exclusiveMinimum"]))
        if "exclusiveMaximum" in field_schema:
            rules.append(ValidationRule("lt", field_schema["exclusiveMaximum"]))
        
        # Array validations
        if "minItems" in field_schema:
            rules.append(ValidationRule("min_items", field_schema["minItems"]))
        if "maxItems" in field_schema:
            rules.append(ValidationRule("max_items", field_schema["maxItems"]))
        
        return rules
    
    def extract_conditional_schemas(self, field_schema: Dict) -> List[ConditionalSchema]:
        """Extract conditional schema logic"""
        conditionals = []
        
        # Handle if/then/else
        if "if" in field_schema:
            conditional = ConditionalSchema(
                condition=field_schema["if"],
                then_schema=field_schema.get("then"),
                else_schema=field_schema.get("else")
            )
            conditionals.append(conditional)
        
        # Handle allOf, oneOf, anyOf (simplified)
        for key in ["allOf", "oneOf", "anyOf"]:
            if key in field_schema:
                for i, sub_schema in enumerate(field_schema[key]):
                    conditional = ConditionalSchema(
                        condition={key: i},
                        then_schema=sub_schema
                    )
                    conditionals.append(conditional)
        
        return conditionals
    
    def get_enhanced_python_type(self, schema_type: str, field_schema: Dict, 
                                is_array: bool = False) -> str:
        """Enhanced type detection with better handling of complex types"""
        format_type = field_schema.get("format")
        enum_values = field_schema.get("enum")
        
        if enum_values:
            enum_name = f"Enum{len(self.enums)}"
            self.enums[enum_name] = enum_values
            base_type = enum_name
        elif schema_type == "string":
            if format_type == "date-time":
                self.imports.add("from datetime import datetime")
                base_type = "datetime"
            elif format_type == "date":
                self.imports.add("from datetime import date")
                base_type = "date"
            elif format_type == "time":
                self.imports.add("from datetime import time")
                base_type = "time"
            elif format_type in ["uri", "uri-reference", "iri", "iri-reference"]:
                self.imports.add("from pydantic import AnyUrl")
                base_type = "AnyUrl"
            elif format_type == "email":
                self.imports.add("from pydantic import EmailStr")
                base_type = "EmailStr"
            elif format_type == "uuid":
                self.imports.add("from uuid import UUID")
                base_type = "UUID"
            else:
                base_type = "str"
        elif schema_type == "integer":
            base_type = "int"
        elif schema_type == "number":
            base_type = "float"
        elif schema_type == "boolean":
            base_type = "bool"
        elif schema_type == "array":
            items_schema = field_schema.get("items", {})
            items_type = items_schema.get("type", "string")
            if items_type == "object":
                base_type = "List[Dict[str, Any]]"  # Will be refined
            else:
                item_type = self.get_enhanced_python_type(items_type, items_schema)
                base_type = f"List[{item_type}]"
            self.imports.add("from typing import List")
        elif schema_type == "object":
            base_type = "Dict[str, Any]"  # Will be refined for nested objects
            self.imports.add("from typing import Dict, Any")
        else:
            # Handle multiple types
            if isinstance(schema_type, list):
                types = [self.get_enhanced_python_type(t, field_schema) for t in schema_type]
                base_type = f"Union[{', '.join(types)}]"
                self.imports.add("from typing import Union")
            else:
                base_type = "Any"
                self.imports.add("from typing import Any")
        
        if is_array and not base_type.startswith("List"):
            self.imports.add("from typing import List")
            return f"List[{base_type}]"
        
        return base_type
    
    def generate_field_with_validation(self, field_info: AdvancedFieldInfo) -> str:
        """Generate field definition with Pydantic validation"""
        field_def = f"{field_info.python_name}: {field_info.type_hint}"
        
        # Add Field() with validation if needed
        field_params = []
        
        # Add description
        if field_info.description:
            desc = field_info.description.replace('"', '\\"').replace('\n', ' ')[:200]
            field_params.append(f'description="{desc}"')
        
        # Add examples
        if field_info.examples:
            example = field_info.examples[0]
            if isinstance(example, str):
                field_params.append(f'example="{example}"')
            else:
                field_params.append(f'example={example}')
        
        # Add validation rules
        for rule in field_info.validation_rules:
            if rule.rule_type == "pattern":
                field_params.append(f'regex=r"{rule.value}"')
            elif rule.rule_type in ["min_length", "max_length", "ge", "le", "gt", "lt"]:
                field_params.append(f'{rule.rule_type}={rule.value}')
            elif rule.rule_type in ["min_items", "max_items"]:
                field_params.append(f'{rule.rule_type}={rule.value}')
        
        if field_params:
            self.imports.add("from pydantic import Field")
            field_def += f" = Field({', '.join(field_params)})"
        elif field_info.default_value:
            field_def += field_info.default_value
        
        return field_def

    def process_advanced_object_schema(self, schema: Dict, class_name: str,
                                     parent_path: str = "") -> AdvancedClassInfo:
        """Process object schema with advanced features"""
        properties = schema.get("properties", {})
        required_fields = set(schema.get("required", []))
        description = schema.get("description", f"Generated class for {class_name}")
        additional_properties = schema.get("additionalProperties", True)
        pattern_properties = schema.get("patternProperties", {})

        fields = []

        for field_name, field_schema in properties.items():
            python_field_name = self.sanitize_name(field_name)
            field_type = field_schema.get("type", "string")
            is_required = field_name in required_fields
            is_array = field_type == "array"

            # Extract validation rules and conditional schemas
            validation_rules = self.extract_validation_rules(field_schema)
            conditional_schemas = self.extract_conditional_schemas(field_schema)
            examples = field_schema.get("examples", [])
            if "example" in field_schema:
                examples.append(field_schema["example"])

            # Handle array items
            if is_array:
                items_schema = field_schema.get("items", {})
                items_type = items_schema.get("type", "string")

                if items_type == "object":
                    # Nested object in array
                    nested_class_name = self.sanitize_class_name(f"{class_name}_{field_name}_item")
                    nested_class = self.process_advanced_object_schema(
                        items_schema, nested_class_name, f"{parent_path}.{field_name}"
                    )
                    self.classes[nested_class_name] = nested_class
                    item_type = nested_class_name
                else:
                    item_type = self.get_enhanced_python_type(items_type, items_schema)

                type_hint = f"Optional[List[{item_type}]]"
                self.imports.add("from typing import List, Optional")
            else:
                if field_type == "object":
                    # Nested object
                    nested_class_name = self.sanitize_class_name(f"{class_name}_{field_name}")
                    nested_class = self.process_advanced_object_schema(
                        field_schema, nested_class_name, f"{parent_path}.{field_name}"
                    )
                    self.classes[nested_class_name] = nested_class
                    type_hint = nested_class_name
                else:
                    type_hint = self.get_enhanced_python_type(field_type, field_schema)

                if not is_required:
                    type_hint = f"Optional[{type_hint}]"
                    self.imports.add("from typing import Optional")

            default_value = " = None" if not is_required else ""

            field_info = AdvancedFieldInfo(
                name=field_name,
                python_name=python_field_name,
                type_hint=type_hint,
                default_value=default_value,
                description=field_schema.get("description", ""),
                is_required=is_required,
                is_array=is_array,
                enum_values=field_schema.get("enum"),
                format_type=field_schema.get("format"),
                validation_rules=validation_rules,
                conditional_schemas=conditional_schemas,
                examples=examples
            )
            fields.append(field_info)

        # Extract class-level conditional schemas
        class_conditionals = self.extract_conditional_schemas(schema)

        return AdvancedClassInfo(
            name=class_name,
            python_name=self.sanitize_class_name(class_name),
            description=description,
            fields=fields,
            required_fields=required_fields,
            parent_path=parent_path,
            conditional_schemas=class_conditionals,
            additional_properties=additional_properties,
            pattern_properties=pattern_properties
        )

    def generate_advanced_class_code(self, class_info: AdvancedClassInfo) -> str:
        """Generate advanced class code with validators"""
        code = f"class {class_info.python_name}(BaseModel):\n"
        code += f'    """\n    {class_info.description}\n    """\n\n'

        # Generate fields with validation
        for field in class_info.fields:
            if field.description:
                # Add field description as docstring comment
                description_lines = field.description.replace('\n', ' ').strip()
                if len(description_lines) > 100:
                    description_lines = description_lines[:97] + "..."
                code += f"    # {description_lines}\n"

            field_def = self.generate_field_with_validation(field)
            code += f"    {field_def}\n"

        # Add custom validators if needed
        validators = self.generate_custom_validators(class_info)
        if validators:
            code += "\n" + validators

        # Add model configuration
        config = self.generate_model_config(class_info)
        if config:
            code += "\n" + config

        code += "\n"
        return code

    def generate_custom_validators(self, class_info: AdvancedClassInfo) -> str:
        """Generate custom Pydantic validators"""
        validators = []

        # Generate validators for conditional schemas
        for i, conditional in enumerate(class_info.conditional_schemas):
            validator_name = f"validate_conditional_{i}"
            validators.append(f"""
    @root_validator
    def {validator_name}(cls, values):
        \"\"\"Validate conditional schema {i}\"\"\"
        # Add conditional validation logic here
        return values""")

        # Generate validators for fields with complex validation
        for field in class_info.fields:
            if field.validation_rules or field.conditional_schemas:
                validator_name = f"validate_{field.python_name}"
                validators.append(f"""
    @validator('{field.python_name}')
    def {validator_name}(cls, v):
        \"\"\"Validate {field.python_name}\"\"\"
        # Add custom validation logic here
        return v""")

        if validators:
            self.imports.add("from pydantic import validator, root_validator")
            return "\n".join(validators)

        return ""

    def generate_model_config(self, class_info: AdvancedClassInfo) -> str:
        """Generate Pydantic model configuration"""
        config_options = []

        if not class_info.additional_properties:
            config_options.append("extra = 'forbid'")

        if class_info.pattern_properties:
            config_options.append("extra = 'allow'")

        if config_options:
            config = "    class Config:\n"
            for option in config_options:
                config += f"        {option}\n"
            return config

        return ""

    def analyze_advanced_schema(self):
        """Analyze schema with advanced features"""
        print("Analyzing advanced schema structure...")

        # Process the schema
        if "properties" in self.schema:
            root_properties = self.schema["properties"]

            # Look for the main object (usually 'dmp' or the root)
            if "dmp" in root_properties:
                dmp_schema = root_properties["dmp"]
                main_class = self.process_advanced_object_schema(dmp_schema, "DMP", "dmp")
                self.classes["DMP"] = main_class
            else:
                # Process the entire schema as the main class
                main_class = self.process_advanced_object_schema(self.schema, "MainClass", "")
                self.classes["MainClass"] = main_class

        print(f"Found {len(self.classes)} classes and {len(self.enums)} enums")

    def generate_advanced_models_file(self) -> str:
        """Generate enhanced models.py file"""
        code = f'"""\nAdvanced data models for {self.library_name}\n"""\n\n'

        # Enhanced imports
        code += "from pydantic import BaseModel, Field, validator, root_validator\n"
        code += "from typing import Optional, List, Dict, Any, Union\n"
        code += "from enum import Enum\n"

        # Add specific imports
        for import_stmt in sorted(self.imports):
            code += f"{import_stmt}\n"

        if self.enums:
            code += "from .enums import *\n"

        code += "\n\n"

        # Generate all classes with advanced features
        for class_name in sorted(self.classes.keys()):
            class_info = self.classes[class_name]
            code += self.generate_advanced_class_code(class_info)
            code += "\n"

        return code

    def generate_test_file(self) -> str:
        """Generate test file for the library"""
        main_class = "DMP" if "DMP" in self.classes else list(self.classes.keys())[0]

        code = f'"""\nTests for {self.library_name}\n"""\n\n'
        code += "import pytest\nimport json\nfrom pathlib import Path\n"
        code += f"from {self.library_name} import {main_class}\n\n"

        code += f"""
def test_{main_class.lower()}_creation():
    \"\"\"Test basic {main_class} creation\"\"\"
    # Add test data here
    data = {{}}
    instance = {main_class}(**data)
    assert instance is not None

def test_{main_class.lower()}_validation():
    \"\"\"Test {main_class} validation\"\"\"
    # Add validation tests here
    pass

def test_{main_class.lower()}_serialization():
    \"\"\"Test {main_class} JSON serialization\"\"\"
    # Add serialization tests here
    pass

def test_{main_class.lower()}_from_file():
    \"\"\"Test loading {main_class} from file\"\"\"
    # Add file loading tests here
    pass
"""
        return code

    def generate_advanced_library(self):
        """Generate the complete advanced library"""
        print(f"Generating advanced Python library '{self.library_name}'...")

        self.load_schema()
        self.analyze_advanced_schema()

        # Create output structure
        self.output_dir.mkdir(parents=True, exist_ok=True)
        lib_dir = self.output_dir / self.library_name
        lib_dir.mkdir(exist_ok=True)

        # Generate files
        files_to_generate = {
            "__init__.py": self.generate_init_file(),
            "models.py": self.generate_advanced_models_file(),
            "utils.py": self.generate_utils_file(),
        }

        if self.enums:
            files_to_generate["enums.py"] = self.generate_enums_file()

        # Write library files
        for filename, content in files_to_generate.items():
            with open(lib_dir / filename, "w", encoding="utf-8") as f:
                f.write(content)

        # Write additional files
        additional_files = {
            "README.md": self.generate_readme(),
            "requirements.txt": "pydantic>=2.0.0\npytest>=7.0.0\n",
            "test_library.py": self.generate_test_file(),
        }

        for filename, content in additional_files.items():
            with open(self.output_dir / filename, "w", encoding="utf-8") as f:
                f.write(content)

        print(f"Successfully generated advanced library!")
        print(f"Location: {self.output_dir}")
        print(f"Library name: {self.library_name}")
        print(f" Classes: {len(self.classes)}")
        print(f"Enums: {len(self.enums)}")
        print(f"Features: Advanced validation, conditional schemas, custom validators")

    # Reuse methods from the basic generator
    def generate_init_file(self) -> str:
        """Generate __init__.py file"""
        # Properly escape the schema file path
        schema_file_escaped = str(self.schema_file).replace('\\', '\\\\')

        code = f'"""\n{self.library_name} - Advanced Generated Library from JSON Schema\n\n'
        code += f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        code += f"Source schema: {schema_file_escaped}\n"
        code += f"Features: Advanced validation, conditional schemas, custom validators\n"
        code += '"""\n\n'

        # Import all classes
        code += "from .models import (\n"
        for class_name in sorted(self.classes.keys()):
            code += f"    {class_name},\n"
        code += ")\n\n"

        # Import utilities
        code += "from .utils import (\n"
        code += "    load_from_json,\n"
        code += "    load_from_file,\n"
        code += "    export_to_json,\n"
        code += "    export_to_file,\n"
        code += "    validate_json,\n"
        code += ")\n\n"

        # Import enums
        if self.enums:
            code += "from .enums import (\n"
            for enum_name in sorted(self.enums.keys()):
                code += f"    {enum_name},\n"
            code += ")\n\n"

        code += f'__version__ = "1.0.0"\n'
        all_exports = list(self.classes.keys()) + list(self.enums.keys()) + [
            "load_from_json", "load_from_file", "export_to_json", "export_to_file", "validate_json"
        ]
        code += f'__all__ = {all_exports}\n'

        return code

    # Reuse utility methods from basic generator
    def generate_enums_file(self) -> str:
        """Generate enums.py file"""
        if not self.enums:
            return ""

        code = f'"""\nEnumerations for {self.library_name}\n"""\n\n'
        code += "from enum import Enum\n\n"

        for enum_name, enum_values in self.enums.items():
            code += f"class {enum_name}(str, Enum):\n"
            code += f'    """Generated enum with {len(enum_values)} values"""\n'

            for value in enum_values:
                member_name = self.sanitize_name(value).upper()
                if member_name and not member_name[0].isdigit():
                    code += f'    {member_name} = "{value}"\n'
                else:
                    code += f'    VALUE_{hash(value) % 1000} = "{value}"\n'

            code += "\n"

        return code

    def generate_utils_file(self) -> str:
        """Generate utility functions"""
        main_class = "DMP" if "DMP" in self.classes else list(self.classes.keys())[0]

        code = f'"""\nUtility functions for {self.library_name}\n"""\n\n'
        code += "import json\nfrom typing import Dict, Any\nfrom .models import *\n\n"

        code += f"""
def load_from_json(json_data: str) -> {main_class}:
    \"\"\"Load {main_class} from JSON string\"\"\"
    data = json.loads(json_data)
    return {main_class}(**data)

def load_from_file(file_path: str) -> {main_class}:
    \"\"\"Load {main_class} from JSON file\"\"\"
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return {main_class}(**data)

def export_to_json(instance: {main_class}, indent: int = 2) -> str:
    \"\"\"Export {main_class} instance to JSON string\"\"\"
    return instance.model_dump_json(indent=indent)

def export_to_file(instance: {main_class}, file_path: str, indent: int = 2):
    \"\"\"Export {main_class} instance to JSON file\"\"\"
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(export_to_json(instance, indent))

def validate_json(json_data: str) -> bool:
    \"\"\"Validate JSON data against the schema\"\"\"
    try:
        load_from_json(json_data)
        return True
    except Exception:
        return False
"""
        return code

    def generate_readme(self) -> str:
        """Generate README.md file"""
        main_class = "DMP" if "DMP" in self.classes else list(self.classes.keys())[0]

        # Properly escape the schema file path for markdown
        schema_file_escaped = str(self.schema_file).replace('\\', '/')

        readme = f"""# {self.library_name}

Advanced auto-generated Python library from JSON Schema with enhanced validation and conditional logic support.

## Features

- ✅ **Pydantic v2** integration with advanced validation
- ✅ **Conditional schemas** (if/then/else, allOf, oneOf, anyOf)
- ✅ **Pattern validation** with regex support
- ✅ **Custom validators** for complex business logic
- ✅ **Nested object** handling with proper type hints
- ✅ **Enum support** with string-based enumerations
- ✅ **Format validation** (email, URI, date-time, etc.)
- ✅ **Array validation** with min/max items
- ✅ **Reference resolution** ($ref support)

## Installation

```bash
pip install -r requirements.txt
```

## Quick Start

```python
import {self.library_name}

# Load from JSON file
data = {self.library_name}.load_from_file('data.json')

# Create new instance with validation
{main_class.lower()} = {self.library_name}.{main_class}(
    # Add required fields here
)

# Export to JSON with proper formatting
json_output = {self.library_name}.export_to_json({main_class.lower()})

# Validate JSON against schema
is_valid = {self.library_name}.validate_json(json_string)
```

## Generated Classes

This library contains {len(self.classes)} classes with advanced validation:

"""
        for class_name, class_info in sorted(self.classes.items()):
            readme += f"- **{class_name}**: {class_info.description[:100]}...\n"

        if self.enums:
            readme += f"\n## Enumerations\n\n{len(self.enums)} enumerations with validation:\n\n"
            for enum_name, enum_values in self.enums.items():
                readme += f"- **{enum_name}**: {len(enum_values)} values\n"

        readme += f"""
## Advanced Features

### Validation Rules
- Pattern matching with regex
- String length constraints
- Numeric range validation
- Array size validation
- Format validation (email, URI, etc.)

### Conditional Logic
- if/then/else schema validation
- allOf/oneOf/anyOf support
- Field dependencies
- Custom business rule validation

## Schema Information

- **Source**: {schema_file_escaped}
- **Generated**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **Classes**: {len(self.classes)}
- **Enums**: {len(self.enums)}
- **Validation Rules**: Advanced Pydantic v2 validation

## Requirements

- Python 3.8+
- pydantic >= 2.0
- pytest >= 7.0 (for testing)

## Testing

```bash
pytest test_library.py -v
```
"""
        return readme


def main():
    """Main entry point for advanced generator"""
    if len(sys.argv) < 3:
        print("Usage: python advanced_schema_generator.py <schema_file> <output_directory> [library_name]")
        print("\nExample:")
        print("  python advanced_schema_generator.py data/schema.json ./generated advanced_lib")
        sys.exit(1)

    schema_file = sys.argv[1]
    output_dir = sys.argv[2]
    library_name = sys.argv[3] if len(sys.argv) > 3 else "advanced_lib"

    if not os.path.exists(schema_file):
        print(f"Schema file not found: {schema_file}")
        sys.exit(1)

    generator = AdvancedSchemaGenerator(schema_file, output_dir, library_name)
    generator.generate_advanced_library()


if __name__ == "__main__":
    main()
