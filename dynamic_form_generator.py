#!/usr/bin/env python3
"""
Dynamic Web Form Generator for Any madmpy-type Library

This tool automatically generates web forms for any library created by the schema generators.
It analyzes the library structure and creates dynamic forms with proper validation.

Usage:
    python dynamic_form_generator.py --library <library_path> --output <webapp_dir> [--app-name <name>]

Example:
    python dynamic_form_generator.py --library ./gcwg_lib/gcwg_madmp --output ./gcwg_webapp --app-name "GCWG DMP Generator"
"""

import argparse
import os
import sys
import importlib.util
import inspect
import json
from pathlib import Path
from typing import Dict, List, Any, Optional, get_type_hints, get_origin, get_args
from datetime import datetime
import re


class LibraryAnalyzer:
    """Analyzes a madmpy-type library to extract form structure"""
    
    def __init__(self, library_path: str):
        self.library_path = Path(library_path)
        self.library_name = self.library_path.name
        self.library = None
        self.classes = {}
        self.enums = {}
        self.main_class = None
        self.form_structure = {}
    
    def load_library(self) -> bool:
        """Load the library dynamically"""
        try:
            # Add library parent to path
            sys.path.insert(0, str(self.library_path.parent))
            
            # Import the library
            self.library = importlib.import_module(self.library_name)
            print(f"Successfully loaded library: {self.library_name}")
            return True
        
        except ImportError as e:
            print(f"Failed to import library: {e}")
            return False
        except Exception as e:
            print(f"Error loading library: {e}")
            return False
    
    def analyze_classes(self):
        """Analyze all classes in the library"""
        print("Analyzing library classes...")
        
        # Get all classes from the library
        for name in dir(self.library):
            obj = getattr(self.library, name)
            
            if inspect.isclass(obj) and hasattr(obj, '__module__'):
                # Check if it's a Pydantic model
                if hasattr(obj, 'model_fields') or hasattr(obj, '__fields__'):
                    self.classes[name] = obj
                    
                    # Identify main class (usually DMP or similar)
                    if name in ['DMP', 'MainClass'] or 'dmp' in name.lower():
                        self.main_class = obj
                        print(f"Found main class: {name}")
                
                # Check if it's an enum
                elif hasattr(obj, '__members__'):
                    self.enums[name] = obj
        
        # If no main class found, use the first class
        if not self.main_class and self.classes:
            self.main_class = list(self.classes.values())[0]
            print(f"Using first class as main: {list(self.classes.keys())[0]}")
        
        print(f"Found {len(self.classes)} classes and {len(self.enums)} enums")
    
    def get_field_info(self, field_name: str, field_obj: Any, class_name: str = "") -> Dict:
        """Extract detailed field information"""
        field_info = {
            'name': field_name,
            'python_name': field_name,
            'type': 'string',
            'required': False,
            'description': '',
            'default': None,
            'enum_values': None,
            'is_array': False,
            'nested_class': None,
            'format': None,
            'validation': {}
        }
        
        # Get field annotation/type
        if hasattr(field_obj, 'annotation'):
            annotation = field_obj.annotation
        else:
            annotation = str(field_obj)
        
        # Parse type information
        field_info.update(self.parse_type_annotation(annotation, field_name, class_name))
        
        # Get field metadata (Pydantic v2)
        if hasattr(field_obj, 'description') and field_obj.description:
            field_info['description'] = field_obj.description
        
        if hasattr(field_obj, 'default') and field_obj.default is not None:
            field_info['default'] = field_obj.default
            field_info['required'] = False
        else:
            field_info['required'] = True
        
        # Get validation constraints
        if hasattr(field_obj, 'constraints'):
            field_info['validation'] = self.extract_validation_constraints(field_obj.constraints)
        
        return field_info
    
    def parse_type_annotation(self, annotation: Any, field_name: str, class_name: str) -> Dict:
        """Parse type annotation to determine field type"""
        info = {
            'type': 'string',
            'is_array': False,
            'nested_class': None,
            'enum_values': None,
            'format': None
        }
        
        annotation_str = str(annotation)
        
        # Handle Optional types
        if 'Optional' in annotation_str or 'Union' in annotation_str:
            # Extract the actual type from Optional[Type] or Union[Type, None]
            if hasattr(annotation, '__args__'):
                for arg in annotation.__args__:
                    if arg != type(None):
                        annotation = arg
                        break
        
        # Handle List types
        if 'List' in annotation_str or 'list' in annotation_str:
            info['is_array'] = True
            if hasattr(annotation, '__args__') and annotation.__args__:
                # Get the item type
                item_type = annotation.__args__[0]
                item_info = self.parse_type_annotation(item_type, field_name, class_name)
                info.update(item_info)
                return info
        
        # Check if it's one of our classes
        if hasattr(annotation, '__name__'):
            class_name_check = annotation.__name__
            if class_name_check in self.classes:
                info['type'] = 'object'
                info['nested_class'] = class_name_check
                return info
            elif class_name_check in self.enums:
                info['type'] = 'enum'
                enum_class = self.enums[class_name_check]
                info['enum_values'] = [member.value for member in enum_class]
                return info
        
        # Basic type mapping
        if 'str' in annotation_str:
            info['type'] = 'string'
            # Check for special formats
            if 'email' in field_name.lower():
                info['format'] = 'email'
            elif 'url' in field_name.lower() or 'uri' in field_name.lower():
                info['format'] = 'url'
            elif 'date' in field_name.lower():
                info['format'] = 'date'
        elif 'int' in annotation_str:
            info['type'] = 'integer'
        elif 'float' in annotation_str:
            info['type'] = 'number'
        elif 'bool' in annotation_str:
            info['type'] = 'boolean'
        elif 'datetime' in annotation_str:
            info['type'] = 'string'
            info['format'] = 'datetime-local'
        elif 'date' in annotation_str:
            info['type'] = 'string'
            info['format'] = 'date'
        
        return info
    
    def extract_validation_constraints(self, constraints: Any) -> Dict:
        """Extract validation constraints from field"""
        validation = {}
        
        if hasattr(constraints, 'min_length'):
            validation['minLength'] = constraints.min_length
        if hasattr(constraints, 'max_length'):
            validation['maxLength'] = constraints.max_length
        if hasattr(constraints, 'ge'):
            validation['min'] = constraints.ge
        if hasattr(constraints, 'le'):
            validation['max'] = constraints.le
        if hasattr(constraints, 'regex'):
            validation['pattern'] = str(constraints.regex)
        
        return validation
    
    def analyze_class_structure(self, cls: Any, class_name: str, depth: int = 0) -> Dict:
        """Recursively analyze class structure"""
        if depth > 5:  # Prevent infinite recursion
            return {'type': 'object', 'properties': {}}
        
        structure = {
            'type': 'object',
            'class_name': class_name,
            'description': getattr(cls, '__doc__', ''),
            'properties': {},
            'required': []
        }
        
        # Get model fields (Pydantic v2)
        if hasattr(cls, 'model_fields'):
            fields = cls.model_fields
        elif hasattr(cls, '__fields__'):
            fields = cls.__fields__
        else:
            return structure
        
        for field_name, field_obj in fields.items():
            field_info = self.get_field_info(field_name, field_obj, class_name)
            
            # If it's a nested class, analyze it recursively
            if field_info['nested_class']:
                nested_class = self.classes.get(field_info['nested_class'])
                if nested_class:
                    field_info['nested_structure'] = self.analyze_class_structure(
                        nested_class, field_info['nested_class'], depth + 1
                    )
            
            structure['properties'][field_name] = field_info
            
            if field_info['required']:
                structure['required'].append(field_name)
        
        return structure
    
    def generate_form_structure(self):
        """Generate the complete form structure"""
        if not self.main_class:
            print("No main class found to generate form")
            return
        
        print(f"Generating form structure for {self.main_class.__name__}...")
        
        self.form_structure = self.analyze_class_structure(
            self.main_class, self.main_class.__name__
        )
        
        print(f"Form structure generated with {len(self.form_structure['properties'])} fields")
    
    def analyze(self) -> bool:
        """Run complete analysis"""
        if not self.load_library():
            return False
        
        self.analyze_classes()
        self.generate_form_structure()
        
        return True


class DynamicWebAppGenerator:
    """Generates a dynamic web application from library analysis"""
    
    def __init__(self, analyzer: LibraryAnalyzer, output_dir: str, app_name: str = "Dynamic DMP Generator"):
        self.analyzer = analyzer
        self.output_dir = Path(output_dir)
        self.app_name = app_name
        self.library_name = analyzer.library_name
    
    def create_output_structure(self):
        """Create the output directory structure"""
        self.output_dir.mkdir(parents=True, exist_ok=True)
        (self.output_dir / "templates").mkdir(exist_ok=True)
        (self.output_dir / "static").mkdir(exist_ok=True)
        (self.output_dir / "static" / "css").mkdir(exist_ok=True)
        (self.output_dir / "static" / "js").mkdir(exist_ok=True)
        print(f"Created output structure: {self.output_dir}")
    
    def generate_flask_app(self) -> str:
        """Generate the main Flask application"""
        main_class_name = self.analyzer.main_class.__name__
        
        app_code = f'''#!/usr/bin/env python3
"""
Dynamic Web Application for {self.library_name}

Auto-generated from library analysis on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

import os
import sys
import json
import datetime
from pathlib import Path
from flask import Flask, render_template, request, jsonify, send_file, flash, redirect, url_for
from io import BytesIO

# Add library to path
LIBRARY_PATH = Path(__file__).parent / "library"
sys.path.insert(0, str(LIBRARY_PATH.parent))

try:
    import {self.library_name}
    print(f"Successfully imported {{self.library_name}}")
except ImportError as e:
    print(f"Failed to import {{self.library_name}}: {{e}}")
    print("Make sure the library is in the 'library' subdirectory")
    sys.exit(1)

app = Flask(__name__)
app.config['SECRET_KEY'] = 'dynamic-form-generator-secret-key-change-in-production'

# Form structure from library analysis
FORM_STRUCTURE = {json.dumps(self.analyzer.form_structure, indent=2)}

@app.route('/')
def index():
    """Main page"""
    return render_template('index.html', 
                         app_name="{self.app_name}",
                         library_name="{self.library_name}",
                         main_class="{main_class_name}")

@app.route('/create', methods=['GET', 'POST'])
def create_form():
    """Dynamic form creation"""
    if request.method == 'POST':
        try:
            # Get form data
            form_data = request.get_json() if request.is_json else request.form.to_dict()
            
            # Process the data recursively
            processed_data = process_form_data(form_data, FORM_STRUCTURE)
            
            # Create instance using the library
            instance = {self.library_name}.{main_class_name}(**processed_data)
            
            # Export to JSON
            result_json = {self.library_name}.export_to_json(instance)
            
            if request.is_json:
                return jsonify({{
                    'success': True,
                    'data': json.loads(result_json),
                    'message': f'{main_class_name} created successfully!'
                }})
            else:
                flash(f'{main_class_name} created successfully!', 'success')
                return render_template('result.html', 
                                     result_json=result_json,
                                     app_name="{self.app_name}")
        
        except Exception as e:
            error_msg = f'Error creating {main_class_name}: {{str(e)}}'
            if request.is_json:
                return jsonify({{'success': False, 'error': error_msg}}), 400
            else:
                flash(error_msg, 'error')
                return redirect(url_for('create_form'))
    
    return render_template('create_form.html', 
                         form_structure=FORM_STRUCTURE,
                         app_name="{self.app_name}",
                         main_class="{main_class_name}")

@app.route('/validate', methods=['POST'])
def validate_data():
    """Validate uploaded data"""
    try:
        if 'file' not in request.files:
            return jsonify({{'error': 'No file uploaded'}}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({{'error': 'No file selected'}}), 400
        
        # Read and validate the file
        file_content = file.read().decode('utf-8')
        
        # Validate using the library
        is_valid = {self.library_name}.validate_json(file_content)
        
        if is_valid:
            data = json.loads(file_content)
            return jsonify({{
                'success': True,
                'message': f'{main_class_name} is valid!',
                'data': data
            }})
        else:
            return jsonify({{'error': 'Validation failed'}}), 400
        
    except json.JSONDecodeError:
        return jsonify({{'error': 'Invalid JSON format'}}), 400
    except Exception as e:
        return jsonify({{'error': f'Validation error: {{str(e)}}'}}), 400

@app.route('/download', methods=['POST'])
def download_data():
    """Download created data as JSON"""
    try:
        data = request.get_json()
        
        # Create a file-like object
        json_str = json.dumps(data, indent=2)
        file_obj = BytesIO(json_str.encode('utf-8'))
        
        return send_file(
            file_obj,
            as_attachment=True,
            download_name=f'{self.library_name.lower()}_data.json',
            mimetype='application/json'
        )
    except Exception as e:
        return jsonify({{'error': str(e)}}), 400

@app.route('/api/form-structure')
def get_form_structure():
    """API endpoint to get form structure"""
    return jsonify(FORM_STRUCTURE)

def process_form_data(form_data: dict, structure: dict, prefix: str = "") -> dict:
    """Process form data according to structure"""
    result = {{}}
    
    for field_name, field_info in structure.get('properties', {{}}).items():
        form_key = f"{{prefix}}{{field_name}}" if prefix else field_name
        
        if field_info['type'] == 'object' and 'nested_structure' in field_info:
            # Handle nested objects
            nested_data = process_form_data(form_data, field_info['nested_structure'], f"{{form_key}}_")
            if nested_data:
                result[field_name] = nested_data
        elif field_info['is_array']:
            # Handle arrays (simplified - collect all matching keys)
            array_items = []
            i = 0
            while f"{{form_key}}_{{i}}" in form_data:
                item_value = form_data[f"{{form_key}}_{{i}}"]
                if item_value:
                    array_items.append(convert_field_value(item_value, field_info))
                i += 1
            if array_items:
                result[field_name] = array_items
        else:
            # Handle simple fields
            if form_key in form_data and form_data[form_key]:
                result[field_name] = convert_field_value(form_data[form_key], field_info)
    
    return result

def convert_field_value(value: str, field_info: dict):
    """Convert form value to appropriate type"""
    if field_info['type'] == 'integer':
        return int(value) if value else None
    elif field_info['type'] == 'number':
        return float(value) if value else None
    elif field_info['type'] == 'boolean':
        return value.lower() in ['true', '1', 'yes', 'on']
    else:
        return value

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
'''
        return app_code

    def generate_base_template(self) -> str:
        """Generate base HTML template"""
        return '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ app_name }}{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/custom.css') }}" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-file-alt"></i> {{ app_name }}
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('create_form') }}">Create</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <main class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </main>

    <footer class="bg-dark text-light text-center py-3 mt-5">
        <div class="container">
            <p>&copy; 2024 {{ app_name }} - Dynamically Generated Web Form</p>
            <p class="small">Library: {{ library_name }}</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/dynamic-forms.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html>'''

    def generate_index_template(self) -> str:
        """Generate index page template"""
        return '''{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="text-center mb-5">
            <h1 class="display-4 mb-3">
                <i class="fas fa-magic text-primary"></i>
                {{ app_name }}
            </h1>
            <p class="lead">Dynamically generated web form for {{ library_name }} library</p>
            <p class="text-muted">Create, validate, and export {{ main_class }} instances</p>
        </div>

        <div class="row g-4">
            <div class="col-md-6">
                <div class="card h-100 shadow-sm">
                    <div class="card-body text-center">
                        <i class="fas fa-plus-circle fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">Create New {{ main_class }}</h5>
                        <p class="card-text">Use the dynamically generated form to create a new {{ main_class }} instance with validation.</p>
                        <a href="{{ url_for('create_form') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create {{ main_class }}
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card h-100 shadow-sm">
                    <div class="card-body text-center">
                        <i class="fas fa-upload fa-3x text-success mb-3"></i>
                        <h5 class="card-title">Validate Data</h5>
                        <p class="card-text">Upload and validate existing {{ main_class }} JSON files against the schema.</p>
                        <button class="btn btn-success" onclick="showValidationModal()">
                            <i class="fas fa-check-circle"></i> Validate File
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-5">
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle"></i> About This Application</h6>
                <p class="mb-0">
                    This web form was automatically generated from the <strong>{{ library_name }}</strong> library.
                    It provides a user-friendly interface for creating and validating {{ main_class }} instances.
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Validation Modal -->
<div class="modal fade" id="validationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Validate {{ main_class }} File</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="validationForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="validationFile" class="form-label">Select JSON File</label>
                        <input type="file" class="form-control" id="validationFile" accept=".json" required>
                    </div>
                    <div id="validationResult"></div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-success" onclick="validateFile()">Validate</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function showValidationModal() {
    new bootstrap.Modal(document.getElementById('validationModal')).show();
}

function validateFile() {
    const fileInput = document.getElementById('validationFile');
    const resultDiv = document.getElementById('validationResult');

    if (!fileInput.files[0]) {
        resultDiv.innerHTML = '<div class="alert alert-danger">Please select a file.</div>';
        return;
    }

    const formData = new FormData();
    formData.append('file', fileInput.files[0]);

    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin"></i> Validating...</div>';

    fetch('/validate', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultDiv.innerHTML = `<div class="alert alert-success"><i class="fas fa-check-circle"></i> ${data.message}</div>`;
        } else {
            resultDiv.innerHTML = `<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> ${data.error}</div>`;
        }
    })
    .catch(error => {
        resultDiv.innerHTML = `<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> Error: ${error.message}</div>`;
    });
}
</script>
{% endblock %}'''

    def generate_dynamic_form_template(self) -> str:
        """Generate dynamic form template"""
        return '''{% extends "base.html" %}

{% block title %}Create {{ main_class }} - {{ app_name }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-10 mx-auto">
        <h2 class="mb-4">
            <i class="fas fa-plus-circle text-primary"></i>
            Create New {{ main_class }}
        </h2>

        <form id="dynamicForm" method="POST">
            <div id="form-container">
                <!-- Dynamic form fields will be generated here -->
            </div>

            <div class="text-center mt-4">
                <button type="submit" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-check"></i> Create {{ main_class }}
                </button>
                <button type="button" class="btn btn-secondary btn-lg" onclick="loadExampleData()">
                    <i class="fas fa-magic"></i> Load Example Data
                </button>
                <button type="button" class="btn btn-outline-info btn-lg" onclick="clearForm()">
                    <i class="fas fa-eraser"></i> Clear Form
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Form structure from Flask
const FORM_STRUCTURE = {{ form_structure | tojson }};

// Generate the dynamic form on page load
document.addEventListener('DOMContentLoaded', function() {
    generateDynamicForm(FORM_STRUCTURE, 'form-container');
});

// Handle form submission
document.getElementById('dynamicForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = collectFormData();

    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating...';
    submitBtn.disabled = true;

    fetch('/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Store result and redirect
            sessionStorage.setItem('creationResult', JSON.stringify(data.data));
            window.location.href = '/create?success=1';
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        alert('Error: ' + error.message);
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

// Check if we're returning from successful creation
if (window.location.search.includes('success=1')) {
    const resultData = sessionStorage.getItem('creationResult');
    if (resultData) {
        const data = JSON.parse(resultData);
        showResult(data);
        sessionStorage.removeItem('creationResult');
    }
}

function showResult(data) {
    document.body.innerHTML = `
        <div class="container mt-4">
            <div class="alert alert-success">
                <h4><i class="fas fa-check-circle"></i> {{ main_class }} Created Successfully!</h4>
            </div>
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5>Generated {{ main_class }}</h5>
                    <button class="btn btn-primary" onclick="downloadResult()">
                        <i class="fas fa-download"></i> Download JSON
                    </button>
                </div>
                <div class="card-body">
                    <pre><code>${JSON.stringify(data, null, 2)}</code></pre>
                </div>
            </div>
            <div class="text-center mt-3">
                <a href="/create" class="btn btn-secondary">Create Another</a>
                <a href="/" class="btn btn-outline-primary">Back to Home</a>
            </div>
        </div>
    `;
}

function downloadResult() {
    const resultData = JSON.parse(sessionStorage.getItem('creationResult') || '{}');
    fetch('/download', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(resultData)
    })
    .then(response => response.blob())
    .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = '{{ library_name.lower() }}_data.json';
        a.click();
        window.URL.revokeObjectURL(url);
    });
}

function loadExampleData() {
    // Generate example data based on form structure
    const exampleData = generateExampleData(FORM_STRUCTURE);
    populateForm(exampleData);
}

function clearForm() {
    document.getElementById('dynamicForm').reset();
}
</script>
{% endblock %}'''

    def generate_result_template(self) -> str:
        """Generate result display template"""
        return '''{% extends "base.html" %}

{% block title %}Result - {{ app_name }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-10 mx-auto">
        <div class="alert alert-success">
            <h4><i class="fas fa-check-circle"></i> {{ main_class }} Created Successfully!</h4>
        </div>

        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5>Generated {{ main_class }}</h5>
                <button class="btn btn-primary" onclick="downloadJSON()">
                    <i class="fas fa-download"></i> Download JSON
                </button>
            </div>
            <div class="card-body">
                <pre><code>{{ result_json }}</code></pre>
            </div>
        </div>

        <div class="text-center mt-3">
            <a href="{{ url_for('create_form') }}" class="btn btn-secondary">Create Another</a>
            <a href="{{ url_for('index') }}" class="btn btn-outline-primary">Back to Home</a>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function downloadJSON() {
    const jsonData = {{ result_json | tojson }};
    fetch('/download', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: jsonData
    })
    .then(response => response.blob())
    .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = '{{ library_name.lower() }}_data.json';
        a.click();
        window.URL.revokeObjectURL(url);
    });
}
</script>
{% endblock %}'''

    def generate_javascript(self) -> str:
        """Generate dynamic form JavaScript"""
        return '''// Dynamic Form Generator JavaScript
// Automatically generates forms from library structure

let fieldCounter = 0;

function generateDynamicForm(structure, containerId, prefix = '') {
    const container = document.getElementById(containerId);
    if (!container) return;

    container.innerHTML = '';

    const properties = structure.properties || {};

    for (const [fieldName, fieldInfo] of Object.entries(properties)) {
        const fieldContainer = createFieldContainer(fieldName, fieldInfo, prefix);
        container.appendChild(fieldContainer);
    }
}

function createFieldContainer(fieldName, fieldInfo, prefix = '') {
    const container = document.createElement('div');
    container.className = 'form-section mb-4';

    const fullFieldName = prefix ? `${prefix}_${fieldName}` : fieldName;

    // Create section header
    const header = document.createElement('div');
    header.className = 'section-header mb-3';

    if (fieldInfo.type === 'object' && fieldInfo.nested_structure) {
        header.innerHTML = `
            <h5 class="section-title">
                <i class="fas fa-folder"></i> ${formatFieldName(fieldName)}
                ${fieldInfo.required ? '<span class="text-danger">*</span>' : ''}
            </h5>
            ${fieldInfo.description ? `<p class="text-muted">${fieldInfo.description}</p>` : ''}
        `;
        container.appendChild(header);

        // Create nested form
        const nestedContainer = document.createElement('div');
        nestedContainer.className = 'nested-form border rounded p-3 bg-light';
        nestedContainer.id = `nested_${fullFieldName}`;
        container.appendChild(nestedContainer);

        generateDynamicForm(fieldInfo.nested_structure, nestedContainer.id, fullFieldName);

    } else if (fieldInfo.is_array) {
        header.innerHTML = `
            <h5 class="section-title d-flex justify-content-between align-items-center">
                <span>
                    <i class="fas fa-list"></i> ${formatFieldName(fieldName)}
                    ${fieldInfo.required ? '<span class="text-danger">*</span>' : ''}
                </span>
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="addArrayItem('${fullFieldName}', ${JSON.stringify(fieldInfo).replace(/"/g, '&quot;')})">
                    <i class="fas fa-plus"></i> Add Item
                </button>
            </h5>
            ${fieldInfo.description ? `<p class="text-muted">${fieldInfo.description}</p>` : ''}
        `;
        container.appendChild(header);

        // Create array container
        const arrayContainer = document.createElement('div');
        arrayContainer.className = 'array-container';
        arrayContainer.id = `array_${fullFieldName}`;
        container.appendChild(arrayContainer);

        // Add first item if required
        if (fieldInfo.required) {
            addArrayItem(fullFieldName, fieldInfo);
        }

    } else {
        // Simple field
        const field = createSimpleField(fieldName, fieldInfo, fullFieldName);
        container.appendChild(field);
    }

    return container;
}

function createSimpleField(fieldName, fieldInfo, fullFieldName) {
    const container = document.createElement('div');
    container.className = 'mb-3';

    // Create label
    const label = document.createElement('label');
    label.className = 'form-label';
    label.setAttribute('for', fullFieldName);
    label.innerHTML = `${formatFieldName(fieldName)} ${fieldInfo.required ? '<span class="text-danger">*</span>' : ''}`;
    container.appendChild(label);

    // Add description
    if (fieldInfo.description) {
        const description = document.createElement('div');
        description.className = 'form-text text-muted mb-2';
        description.textContent = fieldInfo.description;
        container.appendChild(description);
    }

    // Create input field
    const input = createInputField(fieldInfo, fullFieldName);
    container.appendChild(input);

    return container;
}

function createInputField(fieldInfo, fieldName) {
    let input;

    if (fieldInfo.enum_values) {
        // Create select for enum
        input = document.createElement('select');
        input.className = 'form-select';

        // Add empty option if not required
        if (!fieldInfo.required) {
            const emptyOption = document.createElement('option');
            emptyOption.value = '';
            emptyOption.textContent = '-- Select --';
            input.appendChild(emptyOption);
        }

        // Add enum options
        fieldInfo.enum_values.forEach(value => {
            const option = document.createElement('option');
            option.value = value;
            option.textContent = value;
            input.appendChild(option);
        });

    } else if (fieldInfo.type === 'boolean') {
        // Create checkbox for boolean
        const wrapper = document.createElement('div');
        wrapper.className = 'form-check';

        input = document.createElement('input');
        input.type = 'checkbox';
        input.className = 'form-check-input';

        const label = document.createElement('label');
        label.className = 'form-check-label';
        label.textContent = 'Yes';

        wrapper.appendChild(input);
        wrapper.appendChild(label);
        return wrapper;

    } else if (fieldInfo.type === 'string' && fieldInfo.description && fieldInfo.description.length > 100) {
        // Create textarea for long text
        input = document.createElement('textarea');
        input.className = 'form-control';
        input.rows = 3;

    } else {
        // Create regular input
        input = document.createElement('input');
        input.className = 'form-control';

        // Set input type based on field info
        if (fieldInfo.format === 'email') {
            input.type = 'email';
        } else if (fieldInfo.format === 'url') {
            input.type = 'url';
        } else if (fieldInfo.format === 'date') {
            input.type = 'date';
        } else if (fieldInfo.format === 'datetime-local') {
            input.type = 'datetime-local';
        } else if (fieldInfo.type === 'integer') {
            input.type = 'number';
            input.step = '1';
        } else if (fieldInfo.type === 'number') {
            input.type = 'number';
            input.step = 'any';
        } else {
            input.type = 'text';
        }
    }

    // Set common attributes
    input.id = fieldName;
    input.name = fieldName;

    if (fieldInfo.required) {
        input.required = true;
    }

    // Add validation attributes
    if (fieldInfo.validation) {
        if (fieldInfo.validation.minLength) {
            input.minLength = fieldInfo.validation.minLength;
        }
        if (fieldInfo.validation.maxLength) {
            input.maxLength = fieldInfo.validation.maxLength;
        }
        if (fieldInfo.validation.min !== undefined) {
            input.min = fieldInfo.validation.min;
        }
        if (fieldInfo.validation.max !== undefined) {
            input.max = fieldInfo.validation.max;
        }
        if (fieldInfo.validation.pattern) {
            input.pattern = fieldInfo.validation.pattern;
        }
    }

    return input;
}

function addArrayItem(fieldName, fieldInfo) {
    const container = document.getElementById(`array_${fieldName}`);
    if (!container) return;

    const itemIndex = container.children.length;
    const itemContainer = document.createElement('div');
    itemContainer.className = 'array-item border rounded p-3 mb-2 position-relative';

    // Add remove button
    const removeBtn = document.createElement('button');
    removeBtn.type = 'button';
    removeBtn.className = 'btn btn-outline-danger btn-sm position-absolute top-0 end-0 m-2';
    removeBtn.innerHTML = '<i class="fas fa-trash"></i>';
    removeBtn.onclick = () => itemContainer.remove();
    itemContainer.appendChild(removeBtn);

    // Create field for array item
    const itemFieldName = `${fieldName}_${itemIndex}`;

    if (fieldInfo.nested_structure) {
        // Nested object in array
        const nestedContainer = document.createElement('div');
        nestedContainer.id = `nested_${itemFieldName}`;
        itemContainer.appendChild(nestedContainer);
        generateDynamicForm(fieldInfo.nested_structure, nestedContainer.id, itemFieldName);
    } else {
        // Simple array item
        const itemField = createInputField(fieldInfo, itemFieldName);
        itemContainer.appendChild(itemField);
    }

    container.appendChild(itemContainer);
}

function formatFieldName(name) {
    // Convert snake_case to Title Case
    return name.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase());
}

function collectFormData() {
    const form = document.getElementById('dynamicForm');
    const formData = new FormData(form);
    const data = {};

    // Convert FormData to object
    for (const [key, value] of formData.entries()) {
        if (value !== '') {
            data[key] = value;
        }
    }

    return data;
}

function generateExampleData(structure) {
    const data = {};
    const properties = structure.properties || {};

    for (const [fieldName, fieldInfo] of Object.entries(properties)) {
        if (fieldInfo.type === 'string') {
            data[fieldName] = `Example ${formatFieldName(fieldName)}`;
        } else if (fieldInfo.type === 'integer') {
            data[fieldName] = Math.floor(Math.random() * 100);
        } else if (fieldInfo.type === 'number') {
            data[fieldName] = Math.random() * 100;
        } else if (fieldInfo.type === 'boolean') {
            data[fieldName] = Math.random() > 0.5;
        } else if (fieldInfo.enum_values && fieldInfo.enum_values.length > 0) {
            data[fieldName] = fieldInfo.enum_values[0];
        }
    }

    return data;
}

function populateForm(data) {
    for (const [key, value] of Object.entries(data)) {
        const field = document.getElementById(key);
        if (field) {
            if (field.type === 'checkbox') {
                field.checked = value;
            } else {
                field.value = value;
            }
        }
    }
}'''

    def generate_css(self) -> str:
        """Generate custom CSS"""
        return '''.form-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.section-title {
    color: #495057;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.nested-form {
    background: #ffffff;
    border: 1px solid #dee2e6;
}

.array-container {
    min-height: 50px;
}

.array-item {
    background: #ffffff;
    position: relative;
    padding-top: 40px !important;
}

.array-item .btn {
    z-index: 10;
}

.form-check {
    padding-top: 8px;
}

.section-header h5 {
    margin-bottom: 5px;
}

.text-danger {
    color: #dc3545 !important;
}

.btn-outline-primary:hover {
    color: #fff;
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

pre {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    max-height: 400px;
    overflow-y: auto;
}

.navbar-brand {
    font-weight: bold;
}

footer {
    margin-top: auto;
}

.alert {
    border-radius: 8px;
}

.form-text {
    font-size: 0.875em;
}

.position-relative {
    position: relative !important;
}

.position-absolute {
    position: absolute !important;
}

.top-0 {
    top: 0 !important;
}

.end-0 {
    right: 0 !important;
}'''

    def generate_requirements(self) -> str:
        """Generate requirements.txt"""
        return '''flask>=2.3.0
pydantic>=2.0.0
'''

    def copy_library(self):
        """Copy the analyzed library to the webapp directory"""
        library_dest = self.output_dir / "library" / self.library_name
        library_dest.parent.mkdir(parents=True, exist_ok=True)

        # Copy library files
        import shutil
        try:
            if self.analyzer.library_path.is_dir():
                shutil.copytree(self.analyzer.library_path, library_dest, dirs_exist_ok=True)
            print(f"Copied library to {library_dest}")
        except Exception as e:
            print(f"Could not copy library: {e}")
            print(f"   Please manually copy {self.analyzer.library_path} to {library_dest}")

    def generate_webapp(self):
        """Generate the complete web application"""
        print(f"Generating dynamic web application for {self.library_name}...")

        # Create directory structure
        self.create_output_structure()

        # Copy library
        self.copy_library()

        # Generate files
        files_to_generate = {
            "app.py": self.generate_flask_app(),
            "templates/base.html": self.generate_base_template(),
            "templates/index.html": self.generate_index_template(),
            "templates/create_form.html": self.generate_dynamic_form_template(),
            "templates/result.html": self.generate_result_template(),
            "static/js/dynamic-forms.js": self.generate_javascript(),
            "static/css/custom.css": self.generate_css(),
            "requirements.txt": self.generate_requirements(),
        }

        # Write all files
        for file_path, content in files_to_generate.items():
            full_path = self.output_dir / file_path
            full_path.parent.mkdir(parents=True, exist_ok=True)

            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(content)

        # Generate README
        self.generate_webapp_readme()

        print(f"Dynamic web application generated!")
        print(f"Location: {self.output_dir}")
        print(f"To run: cd {self.output_dir} && python app.py")

    def generate_webapp_readme(self):
        """Generate README for the webapp"""
        readme_content = f'''# {self.app_name}

Dynamic web application automatically generated for the **{self.library_name}** library.

## Features

- 🎨 **Dynamic Form Generation**: Automatically creates forms from library structure
- 📝 **Smart Field Types**: Detects and creates appropriate input types
- 🔍 **Validation**: Built-in validation based on library constraints
- 📊 **Nested Objects**: Supports complex nested object structures
- 📋 **Arrays**: Dynamic add/remove for array fields
- 💾 **JSON Export**: Download created instances as JSON
- ✅ **File Validation**: Upload and validate existing JSON files

## Quick Start

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the application:**
   ```bash
   python app.py
   ```

3. **Open in browser:**
   ```
   http://localhost:5000
   ```

## Generated Structure

```
{self.output_dir.name}/
├── app.py                      # Main Flask application
├── templates/                  # HTML templates
│   ├── base.html              # Base layout
│   ├── index.html             # Home page
│   ├── create_form.html       # Dynamic form
│   └── result.html            # Result display
├── static/                     # Static assets
│   ├── css/custom.css         # Custom styles
│   └── js/dynamic-forms.js    # Dynamic form logic
├── library/                    # Copied library
│   └── {self.library_name}/   # Your library files
└── requirements.txt           # Dependencies
```

## Library Information

- **Library Name**: {self.library_name}
- **Main Class**: {self.analyzer.main_class.__name__ if self.analyzer.main_class else 'Unknown'}
- **Classes Found**: {len(self.analyzer.classes)}
- **Enums Found**: {len(self.analyzer.enums)}
- **Generated On**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Usage

### Creating New Instances

1. Navigate to the "Create" page
2. Fill out the dynamically generated form
3. Click "Create" to generate the instance
4. Download the result as JSON

### Validating Existing Data

1. Use the "Validate" feature on the home page
2. Upload a JSON file
3. Get validation results instantly

### Working with Arrays

- Click "Add Item" to add array elements
- Use the trash icon to remove items
- Nested objects in arrays are fully supported

### Example Data

- Click "Load Example Data" to populate the form with sample values
- Modify as needed for your use case

## Customization

### Modifying the Form

The form structure is automatically generated from the library analysis. To modify:

1. Update your library
2. Regenerate the webapp using the dynamic form generator
3. The form will automatically reflect library changes

### Styling

- Edit `static/css/custom.css` for visual customization
- Modify templates in `templates/` for layout changes
- Update `static/js/dynamic-forms.js` for behavior changes

## API Endpoints

- `GET /` - Home page
- `GET,POST /create` - Dynamic form creation
- `POST /validate` - File validation
- `POST /download` - JSON download
- `GET /api/form-structure` - Get form structure as JSON

## Troubleshooting

### Library Import Issues

If you see import errors:

1. Ensure the library is in the `library/` subdirectory
2. Check that all library dependencies are installed
3. Verify the library structure matches expectations

### Form Generation Issues

If forms don't generate correctly:

1. Check the browser console for JavaScript errors
2. Verify the library has proper Pydantic models
3. Ensure the main class is properly detected

### Validation Errors

If validation fails:

1. Check that uploaded JSON matches the expected structure
2. Verify required fields are present
3. Ensure data types match library expectations

## Development

This webapp was automatically generated by the Dynamic Form Generator. To regenerate:

```bash
python dynamic_form_generator.py --library path/to/library --output ./webapp --app-name "My App"
```

## Support

This is an automatically generated application. For issues:

1. Check the library documentation
2. Verify the library is properly structured
3. Regenerate the webapp if the library has been updated
'''

        with open(self.output_dir / "README.md", 'w', encoding='utf-8') as f:
            f.write(readme_content)


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Generate dynamic web form from madmpy-type library",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Generate webapp for GCWG library
  python dynamic_form_generator.py --library ./gcwg_lib/gcwg_madmp --output ./gcwg_webapp --app-name "GCWG DMP Generator"

  # Generate webapp for any madmpy-type library
  python dynamic_form_generator.py --library ./my_lib/my_library --output ./my_webapp --app-name "My Data Generator"
        """
    )

    parser.add_argument("--library", "-l", required=True, help="Path to the madmpy-type library directory")
    parser.add_argument("--output", "-o", required=True, help="Output directory for the web application")
    parser.add_argument("--app-name", "-n", default="Dynamic Form Generator", help="Name of the web application")

    args = parser.parse_args()

    # Validate library path
    library_path = Path(args.library)
    if not library_path.exists():
        print(f"Library path not found: {library_path}")
        sys.exit(1)

    # Analyze the library
    print(f"Analyzing library: {library_path}")
    analyzer = LibraryAnalyzer(str(library_path))

    if not analyzer.analyze():
        print( "Failed to analyze library")
        sys.exit(1)

    # Generate the web application
    generator = DynamicWebAppGenerator(analyzer, args.output, args.app_name)
    generator.generate_webapp()

    print("\n Dynamic web application generated successfully!")
    print(f"Location: {args.output}")
    print(f"To run: cd {args.output} && pip install -r requirements.txt && python app.py")
    print(f"Then open: http://localhost:5000")


if __name__ == "__main__":
    main()
