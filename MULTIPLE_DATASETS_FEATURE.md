# Multiple Datasets Feature in DMP Generator

## 🎯 Overview

The DMP Generator web application now supports creating Data Management Plans with **multiple datasets**, following the RDA-DMP Common Standard specification. This feature allows researchers to document complex projects that involve multiple data sources, collections, or types within a single DMP.

## ✨ Key Features

### 1. **Dynamic Dataset Management**
- ➕ **Add Datasets**: Click "Add Another Dataset" to include additional datasets
- 🗑️ **Remove Datasets**: Remove unwanted datasets (minimum 1 required)
- 🔢 **Auto-numbering**: Datasets are automatically numbered (Dataset #1, #2, etc.)
- 📝 **Individual Forms**: Each dataset has its own complete form section

### 2. **Per-Dataset Information**
Each dataset can have its own:
- **Title** and **Description**
- **Dataset Identifier** (DOI/URL)
- **Technical Resources** required
- **Personal Data** classification (Yes/No/Unknown)
- **Sensitive Data** classification (Yes/No/Unknown)

### 3. **Example Data Loading**
- 🎭 **Multi-Dataset Examples**: "Load Example Data" now creates 2 sample datasets:
  - Climate Data Collection 2024
  - Soil Analysis Dataset
- 📚 **Real-world Examples**: Based on actual research scenarios

## 🔧 Technical Implementation

### Backend Changes (`webapp/app.py`)
```python
# Enhanced dataset handling
if 'datasets' in form_data and isinstance(form_data['datasets'], list):
    # Multiple datasets from JSON (AJAX submission)
    for dataset_data in form_data['datasets']:
        dataset = dmp_module.Dataset(...)
        datasets.append(dataset)
else:
    # Single dataset (backward compatibility)
    dataset = dmp_module.Dataset(...)
    datasets.append(dataset)

# Create DMP with multiple datasets
dmp = dmp_module.DMP(dataset=datasets, ...)
```

### Frontend Changes (`webapp/templates/create_dmp.html`)
- **Dynamic Forms**: JavaScript functions to add/remove dataset forms
- **Data Collection**: `collectDatasetData()` function gathers all dataset information
- **Form Validation**: Ensures at least one complete dataset is provided
- **UI Updates**: Visual indicators for dataset numbers and remove buttons

## 📊 Examples from RDA-DMP Standard

The feature is based on real examples from the RDA-DMP Common Standard:

### Example 1: `ex7-dataset-many.json`
```json
{
  "dmp": {
    "title": "DMP with two datasets",
    "dataset": [
      {
        "title": "Cool data",
        "description": "Data which shows...",
        "type": "document"
      },
      {
        "title": "Another dataset", 
        "description": "Additional research data..."
      }
    ]
  }
}
```

### Example 2: `ex10-fairsharing.json`
Contains a comprehensive dataset with detailed distribution information, demonstrating complex multi-dataset scenarios.

## 🎮 User Experience

### Creating Multiple Datasets
1. **Start with One**: Form begins with one dataset (required)
2. **Add More**: Click "Add Another Dataset" button
3. **Fill Details**: Complete each dataset's information independently
4. **Remove if Needed**: Use trash icon to remove unwanted datasets
5. **Submit**: All datasets are included in the generated DMP

### Visual Indicators
- 📊 **Dataset Counter**: "Dataset #1", "Dataset #2", etc.
- 🔴 **Remove Buttons**: Only shown when multiple datasets exist
- ✅ **Validation**: Required fields highlighted per dataset
- 📝 **Form Sections**: Clear separation between datasets

## 🧪 Testing

### Automated Test (`test_multiple_datasets.py`)
```bash
python test_multiple_datasets.py
```

**Test Results:**
```
✅ Successfully created DMP with 3 datasets!
📊 Dataset titles:
   1. Climate Data Collection 2024
   2. Soil Analysis Dataset
   3. Biodiversity Survey Data
✅ DMP validation successful!
```

### Manual Testing
1. Open the web application
2. Navigate to "Create DMP"
3. Click "Load Example Data (2 Datasets)"
4. Observe two pre-filled dataset forms
5. Add a third dataset manually
6. Submit and verify JSON output contains all datasets

## 📋 Validation Rules

### Dataset Requirements
- **Minimum**: 1 dataset required per DMP
- **Required Fields**: Title, Description, Technical Resource
- **Optional Fields**: Dataset ID, specific data classifications
- **Data Types**: Personal/Sensitive data classifications per dataset

### Form Validation
- Client-side validation prevents submission without required fields
- Server-side validation ensures DMP standard compliance
- Individual dataset validation with specific error messages

## 🔄 Backward Compatibility

The implementation maintains full backward compatibility:
- **Single Dataset**: Still works as before
- **Form Submission**: Handles both single and multiple dataset scenarios
- **API Endpoints**: Same endpoints support both modes
- **JSON Structure**: Follows RDA-DMP standard (always array of datasets)

## 📈 Benefits for Researchers

### 1. **Comprehensive Documentation**
- Document all project datasets in one DMP
- Maintain relationships between related datasets
- Centralized metadata management

### 2. **Compliance**
- Meets funding agency requirements for complex projects
- Follows RDA-DMP Common Standard specification
- Supports institutional DMP policies

### 3. **Efficiency**
- Single DMP for multi-dataset projects
- Reduced administrative overhead
- Consistent metadata across datasets

## 🚀 Future Enhancements

Potential improvements for the multiple datasets feature:

### 1. **Dataset Relationships**
- Link related datasets
- Define dependencies between datasets
- Hierarchical dataset organization

### 2. **Bulk Operations**
- Import datasets from CSV/Excel
- Duplicate dataset forms with modifications
- Batch validation and export

### 3. **Advanced Metadata**
- Dataset-specific contributors
- Individual licensing per dataset
- Detailed distribution information per dataset

### 4. **Templates**
- Save dataset templates for reuse
- Project-specific dataset templates
- Institution-wide dataset standards

## 📚 Resources

- **RDA-DMP Common Standard**: [GitHub Repository](https://github.com/RDA-DMP-Common/RDA-DMP-Common-Standard)
- **madmpy Documentation**: [ReadTheDocs](https://madmpy.readthedocs.io/)
- **Example Files**: Check `data/` directory for multi-dataset examples
- **Web App Guide**: See `WEBAPP_DEPLOYMENT_GUIDE.md`

## 🎉 Conclusion

The multiple datasets feature transforms the DMP Generator from a simple single-dataset tool into a comprehensive research data management solution. It supports the full complexity of modern research projects while maintaining ease of use and standards compliance.

**Ready to use**: The feature is fully implemented and tested, ready for production deployment!
