# gcwg_madmp

Advanced auto-generated Python library from JSON Schema with enhanced validation and conditional logic support.

## Features

- ✅ **Pydantic v2** integration with advanced validation
- ✅ **Conditional schemas** (if/then/else, allOf, oneOf, anyOf)
- ✅ **Pattern validation** with regex support
- ✅ **Custom validators** for complex business logic
- ✅ **Nested object** handling with proper type hints
- ✅ **Enum support** with string-based enumerations
- ✅ **Format validation** (email, URI, date-time, etc.)
- ✅ **Array validation** with min/max items
- ✅ **Reference resolution** ($ref support)

## Installation

```bash
pip install -r requirements.txt
```

## Quick Start

```python
import gcwg_madmp

# Load from JSON file
data = gcwg_madmp.load_from_file('data.json')

# Create new instance with validation
dmp = gcwg_madmp.DMP(
    # Add required fields here
)

# Export to JSON with proper formatting
json_output = gcwg_madmp.export_to_json(dmp)

# Validate JSON against schema
is_valid = gcwg_madmp.validate_json(json_string)
```

## Generated Classes

This library contains 55 classes with advanced validation:

- **DMP**: Generated class for DMP...
- **DmpApproval**: Approval of the maDMP...
- **DmpContact**: Specifies the party which can provide information about the DMP. This is not necessarily the DMP cre...
- **DmpContributorItem**: Party involved in the process of data management described by the DMP, or party involved in the crea...
- **DmpCostItem**: To list costs related to data management. Providing multiple instances of a 'Cost' allows to break d...
- **DmpDatasetItem**: To describe data on a non-technical level....
- **DmpGeneralInfo**: Generated class for DmpGeneralInfo...
- **DmpIndigenousConsiderations**: Indigenous considerations related to the maDMP or the data....
- **DmpProjectItem**: Project related to the DMP...
- **DmpcontactContactId**: Persistent identifier associated with the contact...
- **DmpcontactCountry**: Country of the organization the contact is affiliated to...
- **DmpcontactProvinceState**: Province or state of the location. For Canadian Provinces and Territories use the Data reference sta...
- **DmpcontributoritemAffiliationItem**: Contributor affiliation status with an outside organization...
- **DmpcontributoritemContributorId**: Identifier of the contributor...
- **DmpcontributoritemaffiliationitemCountry**: Country where the organization the contributor is affiliated to, is located...
- **DmpcontributoritemaffiliationitemProvinceState**: Province or state of the organization the contributor is affiliated to. ...
- **DmpcostitemCostDocumentationItem**: Any external material documenting the costing details....
- **DmpdatasetitemCollection**: Information on how the data is collected....
- **DmpdatasetitemComputingEnvironment**: To describe the operational environment used for data collection, processing, analysis and dissemina...
- **DmpdatasetitemDatasetDocumentationItem**: Repeat as many times as needed to list all existing documentation, procedures for data processing, m...
- **DmpdatasetitemDatasetId**: Dataset ID...
- **DmpdatasetitemDispositionActionItem**: Used to document disposition that has been effected in relation to a dataset. Note: when all data as...
- **DmpdatasetitemDispositionPlanning**: Used to document information in anticipation of a future disposition review. 
Note: does not documen...
- **DmpdatasetitemDistributionItem**: To provide technical information on a specific instance of data....
- **DmpdatasetitemIntellectualProperty**: Intellectual property related to the dataset...
- **DmpdatasetitemMetadataItem**: To describe metadata standards used....
- **DmpdatasetitemSecurityAndPrivacyItem**: To list all issues and requirements related to security and privacy. Create a new entry for each iss...
- **DmpdatasetitemSubjectItem**: Topic to which a dataset pertains....
- **DmpdatasetitemTechnicalResourceItem**: To list technical resources involved in the collection and processing of the dataset, and for which ...
- **DmpdatasetitemcomputingenvironmentDataManagementSystem**: To describe the database management system or other computerized data system used to store or manage...
- **DmpdatasetitemcomputingenvironmentHardwareRequirements**: Minimum requirements for processing data for a specific purpose
...
- **DmpdatasetitemcomputingenvironmentSoftwareItem**: Repeat as many times as needed to describe all software and code used for data collection, data proc...
- **DmpdatasetitemcomputingenvironmentsoftwareitemSoftwareManagementPlanItem**: To reference an external software management plan...
- **DmpdatasetitemdispositionplanningDispositionImpedimentItem**: For noting time-limited impediments to effecting disposition at the end of the dataset's retention p...
- **DmpdatasetitemdispositionplanningRetentionSpecificationItem**: Generated class for DmpdatasetitemdispositionplanningRetentionSpecificationItem...
- **DmpdatasetitemdistributionitemDistributionId**: ID for the distribution...
- **DmpdatasetitemdistributionitemGeographicBoundingBox**: Rectangular spatial extent that encompasses all the geographic locations represented in the data...
- **DmpdatasetitemdistributionitemHost**: The host is the system where the data are stored and processed. Be sure to also fill in a physical d...
- **DmpdatasetitemdistributionitemOnlineServiceItem**: To list online services where the distribution can be accessed...
- **DmpdatasetitemdistributionitemPhysicalDataAssetItem**: Allows for recording of physical assets (e.g., external hard drives) and/or physical location of ser...
- **DmpdatasetitemdistributionitemVersionHistory**: Version history of the data distribution...
- **DmpdatasetitemmetadataitemMetadataStandardId**: Metadata Standard ID...
- **DmpdatasetitemsecurityandprivacyitemPrivacyImpactAssessment**: Privacy impact assessment related to the dataset. Government of Canada institutions should refer to ...
- **DmpgeneralinfoDmpId**: Identifier for the DMP itself...
- **DmpgeneralinfoLinkedDmpItem**: to link related dmps , for example official-language-equivalent dmps...
- **DmpgeneralinfolinkeddmpitemLinkedDmpIdItem**: identifier of the related dmp...
- **DmpindigenousconsiderationsCommunityApprovalItem**: to describe approvals by indigenous communities...
- **DmpprojectitemFundingItem**: Funding related with a project...
- **DmpprojectitemPartnerOrganizationItem**: Partner organization...
- **DmpprojectitemSafeguardingScienceMeasures**: Science is defined broadly to include the natural, health, and social sciences, mathematics, enginee...
- **DmpprojectitemfundingitemFunderId**: Funder ID of the associated project...
- **DmpprojectitemfundingitemGrantId**: Grant ID of the associated project...
- **DmpprojectitemfundingitemSourceItem**: Project funding source...
- **DmpprojectitempartnerorganizationitemAgreement**: Partner organization agreement...
- **DmpprojectitempartnerorganizationitemPartnerOrganizationId**: Unique identifier assigned to represent partner organization....

## Enumerations

79 enumerations with validation:

- **Enum0**: 6 values
- **Enum1**: 7490 values
- **Enum2**: 3 values
- **Enum3**: 6 values
- **Enum4**: 7 values
- **Enum5**: 3 values
- **Enum6**: 6 values
- **Enum7**: 4 values
- **Enum8**: 7 values
- **Enum9**: 249 values
- **Enum10**: 6 values
- **Enum11**: 3 values
- **Enum12**: 5 values
- **Enum13**: 2 values
- **Enum14**: 3 values
- **Enum15**: 3 values
- **Enum16**: 3 values
- **Enum17**: 3 values
- **Enum18**: 3 values
- **Enum19**: 3 values
- **Enum20**: 637 values
- **Enum21**: 638 values
- **Enum22**: 5 values
- **Enum23**: 10 values
- **Enum24**: 12 values
- **Enum25**: 19 values
- **Enum26**: 7490 values
- **Enum27**: 3 values
- **Enum28**: 3 values
- **Enum29**: 4 values
- **Enum30**: 17 values
- **Enum31**: 17 values
- **Enum32**: 9 values
- **Enum33**: 9 values
- **Enum34**: 7490 values
- **Enum35**: 2 values
- **Enum36**: 13 values
- **Enum37**: 5 values
- **Enum38**: 6 values
- **Enum39**: 15 values
- **Enum40**: 308 values
- **Enum41**: 308 values
- **Enum42**: 7 values
- **Enum43**: 3 values
- **Enum44**: 5 values
- **Enum45**: 6 values
- **Enum46**: 7 values
- **Enum47**: 5 values
- **Enum48**: 6 values
- **Enum49**: 19 values
- **Enum50**: 1 values
- **Enum51**: 3 values
- **Enum52**: 249 values
- **Enum53**: 7 values
- **Enum54**: 57 values
- **Enum55**: 6 values
- **Enum56**: 7 values
- **Enum57**: 9 values
- **Enum58**: 19 values
- **Enum59**: 1 values
- **Enum60**: 3 values
- **Enum61**: 5 values
- **Enum62**: 249 values
- **Enum63**: 2 values
- **Enum64**: 10 values
- **Enum65**: 5 values
- **Enum66**: 6 values
- **Enum67**: 5 values
- **Enum68**: 3 values
- **Enum69**: 7 values
- **Enum70**: 10 values
- **Enum71**: 6 values
- **Enum72**: 6 values
- **Enum73**: 6 values
- **Enum74**: 6 values
- **Enum75**: 4 values
- **Enum76**: 4 values
- **Enum77**: 15 values
- **Enum78**: 249 values

## Advanced Features

### Validation Rules
- Pattern matching with regex
- String length constraints
- Numeric range validation
- Array size validation
- Format validation (email, URI, etc.)

### Conditional Logic
- if/then/else schema validation
- allOf/oneOf/anyOf support
- Field dependencies
- Custom business rule validation

## Schema Information

- **Source**: data\GCWG-RDA-maDMP-schema.json
- **Generated**: 2025-06-13 10:32:05
- **Classes**: 55
- **Enums**: 79
- **Validation Rules**: Advanced Pydantic v2 validation

## Requirements

- Python 3.8+
- pydantic >= 2.0
- pytest >= 7.0 (for testing)

## Testing

```bash
pytest test_library.py -v
```
