{"dmp": {"title": "DMP for our new project", "description": "This DMP is for our new project.", "language": "eng", "created": "2019-12-06T11:33:05.619Z", "modified": "2019-12-06T11:33:05.619Z", "dmp_id": {"identifier": "https://doi.org/10.0000/00.0.1234", "type": "doi"}, "contact": {"name": "<PERSON>", "mbox": "<EMAIL>", "contact_id": {"identifier": "https://www.tiss.tuwien.ac.at/person/2351952424", "type": "other"}}, "contributor": [{"name": "<PERSON>", "mbox": "<EMAIL>", "contributor_id": {"identifier": "https://orcid.org/0000-0002-0000-0000", "type": "orcid"}, "role": ["ProjectLeader"]}, {"name": "<PERSON>", "mbox": "<EMAIL>", "contributor_id": {"identifier": "https://orcid.org/0000-0002-4929-7875", "type": "orcid"}, "role": ["<PERSON><PERSON><PERSON>", "DataManager"]}, {"name": "<PERSON><PERSON><PERSON>", "mbox": "<EMAIL>", "contributor_id": {"identifier": "https://www.tiss.tuwien.ac.at/person/305962565", "type": "other"}, "role": ["DataCurator"]}], "ethical_issues_exist": "yes", "ethical_issues_report": "https://docs.google.com/document/d/xyz", "ethical_issues_description": "Ethical issues are handled by ...", "project": [{"title": "openEO - a common, open source interface between Earth Observation data infrastructures and front-end applications", "project_id": "https://tiss.tuwien.ac.at/api/pdb/rest/project/v3/1428966", "project_id_type": "HTTP-PDB", "start": "2017-10-01", "end": "2020-09-30", "description": "<p>The capabilities of the latest generation of Earth observation satellites to collect large volumes of diverse and thematically rich data are unprecedented. For exploiting these valuable data sets, many research and industry groups have started to shift their processing into the cloud. Although the functionalities of existing cloud computing solutions largely overlap, there are all custom-made and tailored to the specific data infrastructures. This lack of standards not only makes it hard for end users and application developers to develop generic front-ends, but also to compare the cloud offerings by running the same analysis against different cloud back-ends. To solve this, a common interface that allows end- and intermediate users to query cloud-based back offices and carry out computations on them in a simple way is needed. The openEO project will design such an interface, implement it as an open source community project, bind it to generic analytics front-ends and evaluate it against a set of relevant Earth observation cloud back offices. The openEO interface will consist of three layers of Application Programming Interfaces, namely a core API for finding, accessing, and processing large datasets, a driver APIs to connect to back offices operated by European and worldwide industry, and client APIs for analysing these datasets using R, Python and JavaScript. To demonstrate the capability of the openEO interface, four use cases based chiefly on Sentinel-1 and Sentinel-2 time series will be implemented. openEO will simplify the use of cloud-based processing engines, allow switching between cloud-based back office providers and comparing them, and enable reproducible, open Earth observation science. Thereby, openEO reduces the entry barriers for the adaptation of cloud computing technologies by a broad user community and paves the way for the federation of infrastructure capabilities.</p><p> </p>", "funding": [{"funder_name": "European Commission - Framework Programme", "funder_id": {"identifier": "", "type": "other"}, "grant_id": {"identifier": "EO-2-2017", "type": "other"}}]}], "dataset": [{"dataset_id": {"identifier": "https://hdl.handle.net/0000/00.00000", "type": "handle"}, "title": "Client application", "personal_data": "no", "sensitive_data": "no", "type": "Source code", "description": "Some test scripts", "distribution": [{"title": "Planned distribution", "format": [], "byte_size": **********, "data_access": "open", "license": [{"license_name": "The MIT License (MIT)", "license_ref": "http://opensource.org/licenses/mit-license.php", "start_date": "2020-09-30"}], "host": {"title": "GitHub", "url": "https://www.re3data.org/repository/r3d100010375", "host_id_type": "HTTP-RE3DATA", "description": "GitHub is the best place to share code with friends, co-workers, classmates, and complete strangers. Over three million people use GitHub to build amazing things together. With the collaborative features of GitHub.com, our desktop and mobile apps, and GitHub Enterprise, it has never been easier for individuals and teams to write better code, faster. Originally founded by <PERSON>, <PERSON>, and <PERSON><PERSON> to simplify sharing code, GitHub has grown into the largest code host in the world.", "supports_versioning": "yes", "storage_type": "repository", "pid_system": ["other"]}, "available_until": "2030-09-30"}]}, {"dataset_id": {"identifier": "https://hdl.handle.net/0000/00.00000", "type": "handle"}, "title": "Image collection", "personal_data": "no", "sensitive_data": "yes", "type": "Images", "description": "Images of test object X", "distribution": [{"title": "Planned distribution", "format": [], "byte_size": 50000000000, "data_access": "closed"}]}, {"dataset_id": {"identifier": "https://hdl.handle.net/0000/00.00000", "type": "handle"}, "title": "Interviews", "personal_data": "yes", "sensitive_data": "no", "type": "other", "description": "Audio recordings with test persons", "distribution": [{"title": "Planned distribution", "format": ["audio/mpeg"], "byte_size": 439705600, "data_access": "open", "license": [{"license_name": "Creative Commons Attribution (CC-BY)", "license_ref": "http://creativecommons.org/licenses/by/4.0/", "start_date": "2021-01-01"}], "host": {"title": "<PERSON><PERSON><PERSON>", "url": "https://www.re3data.org/repository/r3d100010468", "host_id_type": "HTTP-RE3DATA", "description": "ZENODO builds and operates a simple and innovative service that enables researchers, scientists, EU projects and institutions to share and showcase multidisciplinary research results (data and publications) that are not part of the existing institutional or subject-based repositories of the research communities.\nZENODO enables researchers, scientists, EU projects and institutions to:\neasily share the long tail of small research results in a wide variety of formats including text, spreadsheets, audio, video, and images across all fields of science.\ndisplay their research results and get credited by making the research results citable and integrate them into existing reporting lines to funding agencies like the European Commission.\neasily access and reuse shared research results.", "supports_versioning": "unknown", "storage_type": "repository", "pid_system": ["doi"]}, "available_until": "2031-08-01"}]}]}}