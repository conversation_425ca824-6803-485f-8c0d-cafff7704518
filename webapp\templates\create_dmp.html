{% extends "base.html" %}

{% block title %}Create DMP{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-10 mx-auto">
        <h2 class="mb-4">
            <i class="fas fa-plus-circle text-primary"></i>
            Create New Data Management Plan
        </h2>
        
        <form id="dmpForm" method="POST">
            <!-- Basic DMP Information -->
            <div class="form-section">
                <h4 class="section-title">
                    <i class="fas fa-info-circle"></i> Basic Information
                </h4>
                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label for="dmp_title" class="form-label">DMP Title *</label>
                            <input type="text" class="form-control" id="dmp_title" name="dmp_title" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="language" class="form-label">Language *</label>
                            <select class="form-select" id="language" name="language" required>
                                <option value="eng" selected>English</option>
                                <option value="spa">Spanish</option>
                                <option value="fra">French</option>
                                <option value="deu">German</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="dmp_description" class="form-label">DMP Description</label>
                    <textarea class="form-control" id="dmp_description" name="dmp_description" rows="3"></textarea>
                </div>
                <div class="mb-3">
                    <label for="dmp_id" class="form-label">DMP Identifier (DOI/URL)</label>
                    <input type="url" class="form-control" id="dmp_id" name="dmp_id" placeholder="https://doi.org/10.15497/rda00039">
                </div>
            </div>

            <!-- Contact Information -->
            <div class="form-section">
                <h4 class="section-title">
                    <i class="fas fa-user"></i> Contact Information
                </h4>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="contact_name" class="form-label">Contact Name *</label>
                            <input type="text" class="form-control" id="contact_name" name="contact_name" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="contact_email" class="form-label">Email Address *</label>
                            <input type="email" class="form-control" id="contact_email" name="contact_email" required>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="contact_orcid" class="form-label">ORCID ID</label>
                    <input type="url" class="form-control" id="contact_orcid" name="contact_orcid" placeholder="https://orcid.org/0000-0000-0000-0000">
                </div>
            </div>

            <!-- Dataset Information -->
            <div class="form-section">
                <h4 class="section-title d-flex justify-content-between align-items-center">
                    <span><i class="fas fa-database"></i> Dataset Information</span>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="addDataset()">
                        <i class="fas fa-plus"></i> Add Another Dataset
                    </button>
                </h4>

                <div id="datasets-container">
                    <!-- First dataset (always present) -->
                    <div class="dataset-form" data-dataset-index="0">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="mb-0">Dataset #1</h5>
                            <button type="button" class="btn btn-outline-danger btn-sm remove-dataset" onclick="removeDataset(0)" style="display: none;">
                                <i class="fas fa-trash"></i> Remove
                            </button>
                        </div>

                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label class="form-label">Dataset Title *</label>
                                    <input type="text" class="form-control dataset-title" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Technical Resource *</label>
                                    <input type="text" class="form-control dataset-technical-resource" required>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Dataset Description *</label>
                            <textarea class="form-control dataset-description" rows="3" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Dataset Identifier (DOI/URL)</label>
                            <input type="url" class="form-control dataset-id" placeholder="https://doi.org/10.25504/FAIRsharing.r3vtvx">
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Contains Personal Data? *</label>
                                    <div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input dataset-personal-data" type="radio" name="personal_data_0" value="no" checked>
                                            <label class="form-check-label">No</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input dataset-personal-data" type="radio" name="personal_data_0" value="yes">
                                            <label class="form-check-label">Yes</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input dataset-personal-data" type="radio" name="personal_data_0" value="unknown">
                                            <label class="form-check-label">Unknown</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Contains Sensitive Data? *</label>
                                    <div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input dataset-sensitive-data" type="radio" name="sensitive_data_0" value="no" checked>
                                            <label class="form-check-label">No</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input dataset-sensitive-data" type="radio" name="sensitive_data_0" value="yes">
                                            <label class="form-check-label">Yes</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input dataset-sensitive-data" type="radio" name="sensitive_data_0" value="unknown">
                                            <label class="form-check-label">Unknown</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <hr class="my-4">
                    </div>
                </div>
            </div>

            <!-- Ethical Considerations -->
            <div class="form-section">
                <h4 class="section-title">
                    <i class="fas fa-balance-scale"></i> Ethical Considerations
                </h4>
                <div class="mb-3">
                    <label class="form-label">Do Ethical Issues Exist? *</label>
                    <div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="ethical_issues_exist" id="ethical_no" value="no" checked>
                            <label class="form-check-label" for="ethical_no">No</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="ethical_issues_exist" id="ethical_yes" value="yes">
                            <label class="form-check-label" for="ethical_yes">Yes</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="ethical_issues_exist" id="ethical_unknown" value="unknown">
                            <label class="form-check-label" for="ethical_unknown">Unknown</label>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="ethical_issues_description" class="form-label">Ethical Issues Description</label>
                    <textarea class="form-control" id="ethical_issues_description" name="ethical_issues_description" rows="3"></textarea>
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="text-center">
                <button type="submit" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-check"></i> Create DMP
                </button>
                <button type="button" class="btn btn-secondary btn-lg" onclick="loadExampleData()">
                    <i class="fas fa-magic"></i> Load Example Data (2 Datasets)
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let datasetCounter = 1;

function loadExampleData() {
    document.getElementById('dmp_title').value = 'Example Research Project DMP';
    document.getElementById('dmp_description').value = 'This is an example Data Management Plan for a research project studying climate change impacts.';
    document.getElementById('contact_name').value = 'Dr. Jane Smith';
    document.getElementById('contact_email').value = '<EMAIL>';
    document.getElementById('contact_orcid').value = 'https://orcid.org/0000-0001-2345-6789';

    // Fill first dataset
    const firstDataset = document.querySelector('.dataset-form[data-dataset-index="0"]');
    firstDataset.querySelector('.dataset-title').value = 'Climate Data Collection 2024';
    firstDataset.querySelector('.dataset-description').value = 'Temperature and precipitation data collected from weather stations across the region.';
    firstDataset.querySelector('.dataset-technical-resource').value = 'Weather Station Network';

    // Add a second example dataset
    addDataset();
    const secondDataset = document.querySelector('.dataset-form[data-dataset-index="1"]');
    secondDataset.querySelector('.dataset-title').value = 'Soil Analysis Dataset';
    secondDataset.querySelector('.dataset-description').value = 'Chemical composition and pH levels of soil samples from various locations.';
    secondDataset.querySelector('.dataset-technical-resource').value = 'Laboratory Equipment';
}

function addDataset() {
    const container = document.getElementById('datasets-container');
    const newIndex = datasetCounter++;

    const datasetHtml = `
        <div class="dataset-form" data-dataset-index="${newIndex}">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0">Dataset #${newIndex + 1}</h5>
                <button type="button" class="btn btn-outline-danger btn-sm remove-dataset" onclick="removeDataset(${newIndex})">
                    <i class="fas fa-trash"></i> Remove
                </button>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="mb-3">
                        <label class="form-label">Dataset Title *</label>
                        <input type="text" class="form-control dataset-title" required>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">Technical Resource *</label>
                        <input type="text" class="form-control dataset-technical-resource" required>
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <label class="form-label">Dataset Description *</label>
                <textarea class="form-control dataset-description" rows="3" required></textarea>
            </div>
            <div class="mb-3">
                <label class="form-label">Dataset Identifier (DOI/URL)</label>
                <input type="url" class="form-control dataset-id" placeholder="https://doi.org/10.25504/FAIRsharing.r3vtvx">
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Contains Personal Data? *</label>
                        <div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input dataset-personal-data" type="radio" name="personal_data_${newIndex}" value="no" checked>
                                <label class="form-check-label">No</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input dataset-personal-data" type="radio" name="personal_data_${newIndex}" value="yes">
                                <label class="form-check-label">Yes</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input dataset-personal-data" type="radio" name="personal_data_${newIndex}" value="unknown">
                                <label class="form-check-label">Unknown</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Contains Sensitive Data? *</label>
                        <div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input dataset-sensitive-data" type="radio" name="sensitive_data_${newIndex}" value="no" checked>
                                <label class="form-check-label">No</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input dataset-sensitive-data" type="radio" name="sensitive_data_${newIndex}" value="yes">
                                <label class="form-check-label">Yes</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input dataset-sensitive-data" type="radio" name="sensitive_data_${newIndex}" value="unknown">
                                <label class="form-check-label">Unknown</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="my-4">
        </div>
    `;

    container.insertAdjacentHTML('beforeend', datasetHtml);
    updateRemoveButtons();
}

function removeDataset(index) {
    const datasetForm = document.querySelector(`.dataset-form[data-dataset-index="${index}"]`);
    if (datasetForm) {
        datasetForm.remove();
        updateRemoveButtons();
        updateDatasetNumbers();
    }
}

function updateRemoveButtons() {
    const datasets = document.querySelectorAll('.dataset-form');
    const removeButtons = document.querySelectorAll('.remove-dataset');

    // Show remove buttons only if there's more than one dataset
    removeButtons.forEach(btn => {
        btn.style.display = datasets.length > 1 ? 'inline-block' : 'none';
    });
}

function updateDatasetNumbers() {
    const datasets = document.querySelectorAll('.dataset-form');
    datasets.forEach((dataset, index) => {
        const title = dataset.querySelector('h5');
        title.textContent = `Dataset #${index + 1}`;
    });
}

function collectDatasetData() {
    const datasets = [];
    const datasetForms = document.querySelectorAll('.dataset-form');

    datasetForms.forEach(form => {
        const title = form.querySelector('.dataset-title').value;
        const description = form.querySelector('.dataset-description').value;
        const technicalResource = form.querySelector('.dataset-technical-resource').value;
        const datasetId = form.querySelector('.dataset-id').value;

        // Get selected radio button values
        const personalData = form.querySelector('.dataset-personal-data:checked').value;
        const sensitiveData = form.querySelector('.dataset-sensitive-data:checked').value;

        if (title && description && technicalResource) {
            datasets.push({
                title: title,
                description: description,
                technical_resource_name: technicalResource,
                dataset_id: datasetId,
                personal_data: personalData,
                sensitive_data: sensitiveData
            });
        }
    });

    return datasets;
}

document.getElementById('dmpForm').addEventListener('submit', function(e) {
    e.preventDefault();

    // Collect basic form data
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);

    // Collect dataset data
    const datasets = collectDatasetData();

    if (datasets.length === 0) {
        alert('Please add at least one dataset with all required fields filled.');
        return;
    }

    // Add datasets to the data object
    data.datasets = datasets;

    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating DMP...';
    submitBtn.disabled = true;

    fetch('/create_dmp', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Store DMP data and redirect to result page
            sessionStorage.setItem('dmpResult', JSON.stringify(data.dmp));
            window.location.href = '/create_dmp?success=1';
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        alert('Error: ' + error.message);
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

// Check if we're returning from a successful creation
if (window.location.search.includes('success=1')) {
    const dmpData = sessionStorage.getItem('dmpResult');
    if (dmpData) {
        const dmp = JSON.parse(dmpData);
        document.body.innerHTML = `
            <div class="container mt-4">
                <div class="alert alert-success">
                    <h4><i class="fas fa-check-circle"></i> DMP Created Successfully!</h4>
                </div>
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5>Generated DMP</h5>
                        <button class="btn btn-primary" onclick="downloadDMP()">
                            <i class="fas fa-download"></i> Download JSON
                        </button>
                    </div>
                    <div class="card-body">
                        <pre><code>${JSON.stringify(dmp, null, 2)}</code></pre>
                    </div>
                </div>
                <div class="text-center mt-3">
                    <a href="/create_dmp" class="btn btn-secondary">Create Another DMP</a>
                    <a href="/" class="btn btn-outline-primary">Back to Home</a>
                </div>
            </div>
        `;
        sessionStorage.removeItem('dmpResult');
    }
}

function downloadDMP() {
    const dmpData = JSON.parse(sessionStorage.getItem('dmpResult') || '{}');
    fetch('/download_dmp', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(dmpData)
    })
    .then(response => response.blob())
    .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = 'dmp.json';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
    });
}
</script>
{% endblock %}
