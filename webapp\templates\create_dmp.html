{% extends "base.html" %}

{% block title %}Create DMP{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-10 mx-auto">
        <h2 class="mb-4">
            <i class="fas fa-plus-circle text-primary"></i>
            Create New Data Management Plan
        </h2>
        
        <form id="dmpForm" method="POST">
            <!-- Basic DMP Information -->
            <div class="form-section">
                <h4 class="section-title">
                    <i class="fas fa-info-circle"></i> Basic Information
                </h4>
                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label for="dmp_title" class="form-label">DMP Title *</label>
                            <input type="text" class="form-control" id="dmp_title" name="dmp_title" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="language" class="form-label">Language *</label>
                            <select class="form-select" id="language" name="language" required>
                                <option value="eng" selected>English</option>
                                <option value="spa">Spanish</option>
                                <option value="fra">French</option>
                                <option value="deu">German</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="dmp_description" class="form-label">DMP Description</label>
                    <textarea class="form-control" id="dmp_description" name="dmp_description" rows="3"></textarea>
                </div>
                <div class="mb-3">
                    <label for="dmp_id" class="form-label">DMP Identifier (DOI/URL)</label>
                    <input type="url" class="form-control" id="dmp_id" name="dmp_id" placeholder="https://doi.org/10.15497/rda00039">
                </div>
            </div>

            <!-- Contact Information -->
            <div class="form-section">
                <h4 class="section-title">
                    <i class="fas fa-user"></i> Contact Information
                </h4>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="contact_name" class="form-label">Contact Name *</label>
                            <input type="text" class="form-control" id="contact_name" name="contact_name" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="contact_email" class="form-label">Email Address *</label>
                            <input type="email" class="form-control" id="contact_email" name="contact_email" required>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="contact_orcid" class="form-label">ORCID ID</label>
                    <input type="url" class="form-control" id="contact_orcid" name="contact_orcid" placeholder="https://orcid.org/0000-0000-0000-0000">
                </div>
            </div>

            <!-- Dataset Information -->
            <div class="form-section">
                <h4 class="section-title">
                    <i class="fas fa-database"></i> Dataset Information
                </h4>
                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label for="dataset_title" class="form-label">Dataset Title *</label>
                            <input type="text" class="form-control" id="dataset_title" name="dataset_title" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="technical_resource_name" class="form-label">Technical Resource *</label>
                            <input type="text" class="form-control" id="technical_resource_name" name="technical_resource_name" required>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="dataset_description" class="form-label">Dataset Description *</label>
                    <textarea class="form-control" id="dataset_description" name="dataset_description" rows="3" required></textarea>
                </div>
                <div class="mb-3">
                    <label for="dataset_id" class="form-label">Dataset Identifier (DOI/URL)</label>
                    <input type="url" class="form-control" id="dataset_id" name="dataset_id" placeholder="https://doi.org/10.25504/FAIRsharing.r3vtvx">
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Contains Personal Data? *</label>
                            <div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="personal_data" id="personal_data_no" value="no" checked>
                                    <label class="form-check-label" for="personal_data_no">No</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="personal_data" id="personal_data_yes" value="yes">
                                    <label class="form-check-label" for="personal_data_yes">Yes</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="personal_data" id="personal_data_unknown" value="unknown">
                                    <label class="form-check-label" for="personal_data_unknown">Unknown</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Contains Sensitive Data? *</label>
                            <div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="sensitive_data" id="sensitive_data_no" value="no" checked>
                                    <label class="form-check-label" for="sensitive_data_no">No</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="sensitive_data" id="sensitive_data_yes" value="yes">
                                    <label class="form-check-label" for="sensitive_data_yes">Yes</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="sensitive_data" id="sensitive_data_unknown" value="unknown">
                                    <label class="form-check-label" for="sensitive_data_unknown">Unknown</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Ethical Considerations -->
            <div class="form-section">
                <h4 class="section-title">
                    <i class="fas fa-balance-scale"></i> Ethical Considerations
                </h4>
                <div class="mb-3">
                    <label class="form-label">Do Ethical Issues Exist? *</label>
                    <div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="ethical_issues_exist" id="ethical_no" value="no" checked>
                            <label class="form-check-label" for="ethical_no">No</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="ethical_issues_exist" id="ethical_yes" value="yes">
                            <label class="form-check-label" for="ethical_yes">Yes</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="ethical_issues_exist" id="ethical_unknown" value="unknown">
                            <label class="form-check-label" for="ethical_unknown">Unknown</label>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="ethical_issues_description" class="form-label">Ethical Issues Description</label>
                    <textarea class="form-control" id="ethical_issues_description" name="ethical_issues_description" rows="3"></textarea>
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="text-center">
                <button type="submit" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-check"></i> Create DMP
                </button>
                <button type="button" class="btn btn-secondary btn-lg" onclick="loadExampleData()">
                    <i class="fas fa-magic"></i> Load Example Data
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function loadExampleData() {
    document.getElementById('dmp_title').value = 'Example Research Project DMP';
    document.getElementById('dmp_description').value = 'This is an example Data Management Plan for a research project studying climate change impacts.';
    document.getElementById('contact_name').value = 'Dr. Jane Smith';
    document.getElementById('contact_email').value = '<EMAIL>';
    document.getElementById('contact_orcid').value = 'https://orcid.org/0000-0001-2345-6789';
    document.getElementById('dataset_title').value = 'Climate Data Collection 2024';
    document.getElementById('dataset_description').value = 'Temperature and precipitation data collected from weather stations across the region.';
    document.getElementById('technical_resource_name').value = 'Weather Station Network';
}

document.getElementById('dmpForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);
    
    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating DMP...';
    submitBtn.disabled = true;
    
    fetch('/create_dmp', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Store DMP data and redirect to result page
            sessionStorage.setItem('dmpResult', JSON.stringify(data.dmp));
            window.location.href = '/create_dmp?success=1';
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        alert('Error: ' + error.message);
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

// Check if we're returning from a successful creation
if (window.location.search.includes('success=1')) {
    const dmpData = sessionStorage.getItem('dmpResult');
    if (dmpData) {
        const dmp = JSON.parse(dmpData);
        document.body.innerHTML = `
            <div class="container mt-4">
                <div class="alert alert-success">
                    <h4><i class="fas fa-check-circle"></i> DMP Created Successfully!</h4>
                </div>
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5>Generated DMP</h5>
                        <button class="btn btn-primary" onclick="downloadDMP()">
                            <i class="fas fa-download"></i> Download JSON
                        </button>
                    </div>
                    <div class="card-body">
                        <pre><code>${JSON.stringify(dmp, null, 2)}</code></pre>
                    </div>
                </div>
                <div class="text-center mt-3">
                    <a href="/create_dmp" class="btn btn-secondary">Create Another DMP</a>
                    <a href="/" class="btn btn-outline-primary">Back to Home</a>
                </div>
            </div>
        `;
        sessionStorage.removeItem('dmpResult');
    }
}

function downloadDMP() {
    const dmpData = JSON.parse(sessionStorage.getItem('dmpResult') || '{}');
    fetch('/download_dmp', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(dmpData)
    })
    .then(response => response.blob())
    .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = 'dmp.json';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
    });
}
</script>
{% endblock %}
