# madmpy API Reference v1.0

!!! warning
    This API is based on an old version of the [RDA-DMP Common Standard](https://github.com/RDA-DMP-Common/RDA-DMP-Common-Standard/releases/tag/v1.0/). Read the latest API reference version of this documentation.

Welcome to the `madmpy` API Reference. This documentation provides a comprehensive overview of the available modules, classes, and methods within the library. The `madmpy` library is designed to facilitate the creation, validation, and management of Data Management Plans (DMPs) based on the [RDA-DMP Common Standard](https://www.rd-alliance.org/groups/dmp-common-standards-wg/outputs/). Whether you are integrating DMP functionalities into your system or exploring the different components of a DMP, this reference will help you understand and use the structures and parameters effectively.

::: src.madmpy.v1_0.dmp


