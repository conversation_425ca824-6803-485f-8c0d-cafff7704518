#!/usr/bin/env python3
"""
Test script for the Dynamic Form Generator

This script tests the dynamic form generator with various libraries and scenarios.
"""

import os
import sys
import subprocess
import tempfile
import shutil
from pathlib import Path


def test_with_existing_madmpy():
    """Test with the existing madmpy library"""
    print("🧪 Testing with existing madmpy library...")
    
    # Check if madmpy is available
    try:
        import madmpy
        print("✅ Found existing madmpy library")
        
        # Get madmpy location
        madmpy_path = Path(madmpy.__file__).parent
        print(f"📁 madmpy location: {madmpy_path}")
        
        # Generate webapp
        output_dir = "test_madmpy_webapp"
        cmd = [
            sys.executable, "dynamic_form_generator.py",
            "--library", str(madmpy_path),
            "--output", output_dir,
            "--app-name", "Test madmpy DMP Generator"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ Successfully generated webapp for madmpy")
            print(f"📁 Output: {output_dir}")
            return True
        else:
            print(f"❌ Failed to generate webapp: {result.stderr}")
            return False
    
    except ImportError:
        print("⚠️  madmpy not found - skipping this test")
        return True
    except Exception as e:
        print(f"❌ Error testing with madmpy: {e}")
        return False


def test_with_generated_library():
    """Test with a library generated by our schema generators"""
    print("🧪 Testing with generated library...")
    
    # Check if we have a generated library
    test_libs = ["generated_basic", "generated_advanced", "gcwg_lib"]
    
    for lib_dir in test_libs:
        if Path(lib_dir).exists():
            # Find the actual library subdirectory
            lib_path = Path(lib_dir)
            for subdir in lib_path.iterdir():
                if subdir.is_dir() and not subdir.name.startswith('.'):
                    library_path = subdir
                    break
            else:
                continue
            
            print(f"📁 Found generated library: {library_path}")
            
            # Generate webapp
            output_dir = f"test_{library_path.name}_webapp"
            cmd = [
                sys.executable, "dynamic_form_generator.py",
                "--library", str(library_path),
                "--output", output_dir,
                "--app-name", f"Test {library_path.name} Generator"
            ]
            
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
                
                if result.returncode == 0:
                    print(f"✅ Successfully generated webapp for {library_path.name}")
                    print(f"📁 Output: {output_dir}")
                    return True
                else:
                    print(f"❌ Failed to generate webapp: {result.stderr}")
            except subprocess.TimeoutExpired:
                print("❌ Generation timed out")
            except Exception as e:
                print(f"❌ Error: {e}")
    
    print("⚠️  No generated libraries found - run the schema generators first")
    return True


def create_simple_test_library():
    """Create a simple test library for testing"""
    print("🏗️ Creating simple test library...")
    
    test_lib_dir = Path("test_simple_lib")
    test_lib_dir.mkdir(exist_ok=True)
    
    # Create __init__.py
    init_content = '''"""Simple test library for dynamic form generator"""

from .models import Person, Address

def load_from_json(json_str):
    import json
    data = json.loads(json_str)
    return Person(**data)

def export_to_json(instance):
    return instance.model_dump_json(indent=2)

def validate_json(json_str):
    try:
        load_from_json(json_str)
        return True
    except:
        return False

__all__ = ['Person', 'Address', 'load_from_json', 'export_to_json', 'validate_json']
'''
    
    # Create models.py
    models_content = '''"""Simple test models"""

from pydantic import BaseModel, Field
from typing import Optional, List
from enum import Enum

class StatusEnum(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING = "pending"

class Address(BaseModel):
    """Address information"""
    street: str = Field(description="Street address")
    city: str = Field(description="City name")
    postal_code: Optional[str] = Field(None, description="Postal/ZIP code")
    country: str = Field(description="Country name")

class Person(BaseModel):
    """Person information"""
    name: str = Field(description="Full name of the person")
    email: str = Field(description="Email address")
    age: Optional[int] = Field(None, ge=0, le=150, description="Age in years")
    status: StatusEnum = Field(StatusEnum.ACTIVE, description="Account status")
    addresses: Optional[List[Address]] = Field(None, description="List of addresses")
    is_active: bool = Field(True, description="Whether the person is active")
    bio: Optional[str] = Field(None, description="Biography or description of the person")
'''
    
    # Write files
    with open(test_lib_dir / "__init__.py", "w") as f:
        f.write(init_content)
    
    with open(test_lib_dir / "models.py", "w") as f:
        f.write(models_content)
    
    print(f"✅ Created simple test library: {test_lib_dir}")
    return test_lib_dir


def test_with_simple_library():
    """Test with the simple test library"""
    print("🧪 Testing with simple test library...")
    
    # Create the test library
    lib_path = create_simple_test_library()
    
    # Generate webapp
    output_dir = "test_simple_webapp"
    cmd = [
        sys.executable, "dynamic_form_generator.py",
        "--library", str(lib_path),
        "--output", output_dir,
        "--app-name", "Simple Test Form Generator"
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ Successfully generated webapp for simple library")
            print(f"📁 Output: {output_dir}")
            
            # Test if the webapp can be imported
            test_webapp_import(output_dir)
            return True
        else:
            print(f"❌ Failed to generate webapp: {result.stderr}")
            return False
    
    except subprocess.TimeoutExpired:
        print("❌ Generation timed out")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_webapp_import(webapp_dir):
    """Test if the generated webapp can be imported"""
    print(f"🧪 Testing webapp import: {webapp_dir}")
    
    app_file = Path(webapp_dir) / "app.py"
    if not app_file.exists():
        print("❌ app.py not found")
        return False
    
    # Check if required files exist
    required_files = [
        "templates/base.html",
        "templates/index.html", 
        "templates/create_form.html",
        "static/js/dynamic-forms.js",
        "static/css/custom.css",
        "requirements.txt"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not (Path(webapp_dir) / file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    
    print("✅ All required webapp files present")
    return True


def run_integration_test():
    """Run a complete integration test"""
    print("🚀 Running integration test...")
    
    # Step 1: Generate a library from schema
    schema_file = "data/GCWG-RDA-maDMP-schema.json"
    if Path(schema_file).exists():
        print("📊 Generating library from GCWG schema...")
        
        cmd = [
            sys.executable, "advanced_schema_generator.py",
            schema_file,
            "integration_test_lib",
            "integration_test"
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
            if result.returncode != 0:
                print(f"❌ Library generation failed: {result.stderr}")
                return False
        except subprocess.TimeoutExpired:
            print("❌ Library generation timed out")
            return False
        
        # Step 2: Generate webapp from the library
        print("🎨 Generating webapp from library...")
        
        lib_path = Path("integration_test_lib") / "integration_test"
        cmd = [
            sys.executable, "dynamic_form_generator.py",
            "--library", str(lib_path),
            "--output", "integration_test_webapp",
            "--app-name", "Integration Test DMP Generator"
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
            if result.returncode == 0:
                print("✅ Integration test successful!")
                print("📁 Generated: integration_test_webapp")
                return True
            else:
                print(f"❌ Webapp generation failed: {result.stderr}")
                return False
        except subprocess.TimeoutExpired:
            print("❌ Webapp generation timed out")
            return False
    
    else:
        print(f"⚠️  Schema file not found: {schema_file}")
        return True


def cleanup_test_files():
    """Clean up test files"""
    print("🧹 Cleaning up test files...")
    
    test_dirs = [
        "test_madmpy_webapp",
        "test_simple_lib",
        "test_simple_webapp",
        "integration_test_lib",
        "integration_test_webapp"
    ]
    
    for test_dir in test_dirs:
        if Path(test_dir).exists():
            try:
                shutil.rmtree(test_dir)
                print(f"🗑️  Removed: {test_dir}")
            except Exception as e:
                print(f"⚠️  Could not remove {test_dir}: {e}")


def main():
    """Main test function"""
    print("🧪 Dynamic Form Generator - Test Suite")
    print("=" * 50)
    
    tests = [
        ("Simple Library Test", test_with_simple_library),
        ("Existing madmpy Test", test_with_existing_madmpy),
        ("Generated Library Test", test_with_generated_library),
        ("Integration Test", run_integration_test),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running: {test_name}")
        print("-" * 30)
        
        try:
            success = test_func()
            results[test_name] = success
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"Result: {status}")
        except Exception as e:
            print(f"❌ ERROR: {e}")
            results[test_name] = False
    
    # Summary
    print("\n📋 Test Summary")
    print("=" * 50)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Dynamic form generator is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    # Ask about cleanup
    try:
        cleanup = input("\n🧹 Clean up test files? (y/n): ").lower().strip()
        if cleanup in ['y', 'yes']:
            cleanup_test_files()
    except KeyboardInterrupt:
        print("\n👋 Test completed.")


if __name__ == "__main__":
    main()
