"""
Utility functions for gcwg_madmp
"""

import json
from typing import Dict, Any
from .models import *


def load_from_json(json_data: str) -> DMP:
    """Load DMP from JSON string"""
    data = json.loads(json_data)
    return DMP(**data)

def load_from_file(file_path: str) -> DMP:
    """Load DMP from JSON file"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return DMP(**data)

def export_to_json(instance: DMP, indent: int = 2) -> str:
    """Export DMP instance to JSON string"""
    return instance.model_dump_json(indent=indent)

def export_to_file(instance: DMP, file_path: str, indent: int = 2):
    """Export DMP instance to JSON file"""
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(export_to_json(instance, indent))

def validate_json(json_data: str) -> bool:
    """Validate JSON data against the schema"""
    try:
        load_from_json(json_data)
        return True
    except Exception:
        return False
