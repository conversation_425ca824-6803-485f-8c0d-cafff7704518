#!/usr/bin/env python3
"""
Test script for the JSON Schema to Python Library Generator

This script demonstrates how to use the generators and tests them with the GCWG schema.
"""

import os
import sys
import subprocess
import json
from pathlib import Path


def test_basic_generator():
    """Test the basic schema generator"""
    print("🧪 Testing Basic Schema Generator...")
    
    schema_file = "data/GCWG-RDA-maDMP-schema.json"
    output_dir = "generated_basic"
    library_name = "gcwg_basic"
    
    if not os.path.exists(schema_file):
        print(f"❌ Schema file not found: {schema_file}")
        return False
    
    try:
        # Run the basic generator
        cmd = [sys.executable, "schema_to_library_generator.py", schema_file, output_dir, library_name]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ Basic generator completed successfully")
            print(f"📁 Output: {output_dir}")
            return True
        else:
            print(f"❌ Basic generator failed: {result.stderr}")
            return False
    
    except subprocess.TimeoutExpired:
        print("❌ Basic generator timed out")
        return False
    except Exception as e:
        print(f"❌ Error running basic generator: {e}")
        return False


def test_advanced_generator():
    """Test the advanced schema generator"""
    print("🧪 Testing Advanced Schema Generator...")
    
    schema_file = "data/GCWG-RDA-maDMP-schema.json"
    output_dir = "generated_advanced"
    library_name = "gcwg_advanced"
    
    if not os.path.exists(schema_file):
        print(f"❌ Schema file not found: {schema_file}")
        return False
    
    try:
        # Run the advanced generator
        cmd = [sys.executable, "advanced_schema_generator.py", schema_file, output_dir, library_name]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✅ Advanced generator completed successfully")
            print(f"📁 Output: {output_dir}")
            return True
        else:
            print(f"❌ Advanced generator failed: {result.stderr}")
            return False
    
    except subprocess.TimeoutExpired:
        print("❌ Advanced generator timed out")
        return False
    except Exception as e:
        print(f"❌ Error running advanced generator: {e}")
        return False


def analyze_schema_complexity():
    """Analyze the complexity of the GCWG schema"""
    print("📊 Analyzing GCWG Schema Complexity...")
    
    schema_file = "data/GCWG-RDA-maDMP-schema.json"
    
    if not os.path.exists(schema_file):
        print(f"❌ Schema file not found: {schema_file}")
        return
    
    with open(schema_file, 'r', encoding='utf-8') as f:
        schema = json.load(f)
    
    def count_objects(obj, depth=0):
        """Recursively count objects and arrays in the schema"""
        counts = {"objects": 0, "arrays": 0, "enums": 0, "max_depth": depth}
        
        if isinstance(obj, dict):
            if obj.get("type") == "object":
                counts["objects"] += 1
            elif obj.get("type") == "array":
                counts["arrays"] += 1
            
            if "enum" in obj:
                counts["enums"] += 1
            
            for value in obj.values():
                sub_counts = count_objects(value, depth + 1)
                counts["objects"] += sub_counts["objects"]
                counts["arrays"] += sub_counts["arrays"]
                counts["enums"] += sub_counts["enums"]
                counts["max_depth"] = max(counts["max_depth"], sub_counts["max_depth"])
        
        elif isinstance(obj, list):
            for item in obj:
                sub_counts = count_objects(item, depth + 1)
                counts["objects"] += sub_counts["objects"]
                counts["arrays"] += sub_counts["arrays"]
                counts["enums"] += sub_counts["enums"]
                counts["max_depth"] = max(counts["max_depth"], sub_counts["max_depth"])
        
        return counts
    
    complexity = count_objects(schema)
    
    print(f"📈 Schema Complexity Analysis:")
    print(f"   - Total Objects: {complexity['objects']}")
    print(f"   - Total Arrays: {complexity['arrays']}")
    print(f"   - Total Enums: {complexity['enums']}")
    print(f"   - Max Nesting Depth: {complexity['max_depth']}")
    print(f"   - File Size: {os.path.getsize(schema_file):,} bytes")
    
    # Count properties in the main DMP object
    if "properties" in schema and "dmp" in schema["properties"]:
        dmp_props = schema["properties"]["dmp"].get("properties", {})
        print(f"   - DMP Properties: {len(dmp_props)}")
        
        # Count dataset properties
        if "dataset" in dmp_props and "items" in dmp_props["dataset"]:
            dataset_props = dmp_props["dataset"]["items"].get("properties", {})
            print(f"   - Dataset Properties: {len(dataset_props)}")


def test_generated_library(library_path: str):
    """Test the generated library"""
    print(f"🧪 Testing Generated Library: {library_path}")
    
    if not os.path.exists(library_path):
        print(f"❌ Library path not found: {library_path}")
        return False
    
    try:
        # Add the library to Python path
        sys.path.insert(0, str(Path(library_path).parent))
        
        # Try to import the library
        library_name = Path(library_path).name
        lib = __import__(library_name)
        
        print(f"✅ Successfully imported {library_name}")
        
        # Check for main classes
        if hasattr(lib, 'DMP'):
            print("✅ DMP class found")
        else:
            print("⚠️  DMP class not found")
        
        # Check for utility functions
        utils_found = 0
        for func in ['load_from_json', 'export_to_json', 'validate_json']:
            if hasattr(lib, func):
                utils_found += 1
        
        print(f"✅ Found {utils_found}/3 utility functions")
        
        return True
    
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing library: {e}")
        return False


def create_sample_schema():
    """Create a simple sample schema for testing"""
    sample_schema = {
        "$schema": "http://json-schema.org/draft-07/schema#",
        "title": "Sample Schema",
        "type": "object",
        "properties": {
            "person": {
                "type": "object",
                "properties": {
                    "name": {
                        "type": "string",
                        "description": "Person's full name",
                        "minLength": 1,
                        "maxLength": 100
                    },
                    "age": {
                        "type": "integer",
                        "description": "Person's age",
                        "minimum": 0,
                        "maximum": 150
                    },
                    "email": {
                        "type": "string",
                        "format": "email",
                        "description": "Email address"
                    },
                    "status": {
                        "type": "string",
                        "enum": ["active", "inactive", "pending"],
                        "description": "Account status"
                    },
                    "addresses": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "street": {"type": "string"},
                                "city": {"type": "string"},
                                "country": {"type": "string"}
                            },
                            "required": ["street", "city"]
                        }
                    }
                },
                "required": ["name", "email"]
            }
        },
        "required": ["person"]
    }
    
    with open("sample_schema.json", "w") as f:
        json.dump(sample_schema, f, indent=2)
    
    print("✅ Created sample schema: sample_schema.json")


def test_sample_schema():
    """Test generators with the sample schema"""
    print("🧪 Testing with Sample Schema...")
    
    create_sample_schema()
    
    # Test basic generator
    try:
        cmd = [sys.executable, "schema_to_library_generator.py", "sample_schema.json", "sample_basic", "sample_lib"]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print("✅ Basic generator works with sample schema")
        else:
            print(f"❌ Basic generator failed with sample: {result.stderr}")
    except Exception as e:
        print(f"❌ Error with basic generator: {e}")
    
    # Test advanced generator
    try:
        cmd = [sys.executable, "advanced_schema_generator.py", "sample_schema.json", "sample_advanced", "sample_advanced_lib"]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print("✅ Advanced generator works with sample schema")
        else:
            print(f"❌ Advanced generator failed with sample: {result.stderr}")
    except Exception as e:
        print(f"❌ Error with advanced generator: {e}")


def main():
    """Main test function"""
    print("🚀 JSON Schema to Python Library Generator - Test Suite")
    print("=" * 60)
    
    # Analyze schema complexity first
    analyze_schema_complexity()
    print()
    
    # Test with sample schema first
    test_sample_schema()
    print()
    
    # Test basic generator
    basic_success = test_basic_generator()
    print()
    
    # Test advanced generator
    advanced_success = test_advanced_generator()
    print()
    
    # Test generated libraries
    if basic_success:
        test_generated_library("generated_basic/gcwg_basic")
        print()
    
    if advanced_success:
        test_generated_library("generated_advanced/gcwg_advanced")
        print()
    
    # Summary
    print("📋 Test Summary:")
    print(f"   - Basic Generator: {'✅ PASS' if basic_success else '❌ FAIL'}")
    print(f"   - Advanced Generator: {'✅ PASS' if advanced_success else '❌ FAIL'}")
    
    if basic_success or advanced_success:
        print("\n🎉 At least one generator is working!")
        print("📖 Check the generated README.md files for usage instructions.")
    else:
        print("\n❌ Both generators failed. Check the error messages above.")


if __name__ == "__main__":
    main()
