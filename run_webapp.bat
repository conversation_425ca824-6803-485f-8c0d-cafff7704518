@echo off
echo DMP Generator Web Application - Windows Launcher
echo ================================================

REM Check if Python is available
REM python --version >nul 2>&1
REM if errorlevel 1 (
REM    echo Error: Python is not installed or not in PATH
REM    echo Please install Python 3.11 or higher from https://python.org
REM    pause
REM    exit /b 1
REM )

echo Installing dependencies...
pip install -r webapp_requirements.txt
if errorlevel 1 (
    echo Error: Failed to install dependencies
    pause
    exit /b 1
)

pip install -e .
if errorlevel 1 (
    echo Error: Failed to install madmpy
    pause
    exit /b 1
)

echo.
echo Starting DMP Generator Web Application...
echo Application will be available at: http://localhost:5000
echo Press Ctrl+C to stop the server
echo.

cd webapp
python app.py

pause
