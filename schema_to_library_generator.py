#!/usr/bin/env python3
"""
JSON Schema to Python Library Generator

This tool automatically generates a Python library similar to madmpy from any JSON schema.
It handles complex schemas with nested structures, arrays, enums, and conditional logic.

Usage:
    python schema_to_library_generator.py <schema_file> <output_directory> [library_name]

Example:
    python schema_to_library_generator.py data/GCWG-RDA-maDMP-schema.json generated_lib gcwg_madmp
"""

import json
import os
import sys
import re
from pathlib import Path
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass
from datetime import datetime


@dataclass
class FieldInfo:
    """Information about a field in the schema"""
    name: str
    python_name: str
    type_hint: str
    default_value: str
    description: str
    is_required: bool
    is_array: bool
    enum_values: Optional[List[str]] = None
    format_type: Optional[str] = None


@dataclass
class ClassInfo:
    """Information about a class to be generated"""
    name: str
    python_name: str
    description: str
    fields: List[FieldInfo]
    required_fields: Set[str]
    parent_path: str


class SchemaToLibraryGenerator:
    """Main generator class"""
    
    def __init__(self, schema_file: str, output_dir: str, library_name: str = "generated_lib"):
        self.schema_file = schema_file
        self.output_dir = Path(output_dir)
        self.library_name = library_name
        self.schema = {}
        self.classes = {}
        self.enums = {}
        self.imports = set()
        
        # Reserved Python keywords to avoid
        self.reserved_keywords = {
            'class', 'def', 'if', 'else', 'elif', 'while', 'for', 'try', 'except',
            'finally', 'with', 'as', 'import', 'from', 'return', 'yield', 'lambda',
            'global', 'nonlocal', 'assert', 'del', 'pass', 'break', 'continue',
            'and', 'or', 'not', 'in', 'is', 'True', 'False', 'None', 'type'
        }
    
    def load_schema(self):
        """Load and parse the JSON schema"""
        with open(self.schema_file, 'r', encoding='utf-8') as f:
            self.schema = json.load(f)
        print(f"✅ Loaded schema from {self.schema_file}")
    
    def sanitize_name(self, name: str) -> str:
        """Convert schema names to valid Python identifiers"""
        # Remove special characters and convert to snake_case
        name = re.sub(r'[^a-zA-Z0-9_]', '_', name)
        name = re.sub(r'([a-z0-9])([A-Z])', r'\1_\2', name).lower()
        name = re.sub(r'_+', '_', name).strip('_')
        
        # Handle reserved keywords
        if name in self.reserved_keywords:
            name = f"{name}_field"
        
        # Ensure it starts with a letter
        if name and name[0].isdigit():
            name = f"field_{name}"
        
        return name or "unknown_field"
    
    def sanitize_class_name(self, name: str) -> str:
        """Convert schema names to valid Python class names (PascalCase)"""
        name = re.sub(r'[^a-zA-Z0-9_]', '_', name)
        parts = name.split('_')
        class_name = ''.join(word.capitalize() for word in parts if word)
        
        # Ensure it starts with a letter
        if class_name and class_name[0].isdigit():
            class_name = f"Class{class_name}"
        
        return class_name or "UnknownClass"
    
    def get_python_type(self, schema_type: str, format_type: str = None, 
                       enum_values: List[str] = None, is_array: bool = False) -> str:
        """Convert JSON schema types to Python type hints"""
        if enum_values:
            enum_name = f"Enum{len(self.enums)}"
            self.enums[enum_name] = enum_values
            base_type = enum_name
        elif schema_type == "string":
            if format_type == "date-time":
                self.imports.add("from datetime import datetime")
                base_type = "datetime"
            elif format_type == "date":
                self.imports.add("from datetime import date")
                base_type = "date"
            elif format_type == "uri":
                self.imports.add("from pydantic import AnyUrl")
                base_type = "AnyUrl"
            else:
                base_type = "str"
        elif schema_type == "integer":
            base_type = "int"
        elif schema_type == "number":
            base_type = "float"
        elif schema_type == "boolean":
            base_type = "bool"
        elif schema_type == "array":
            base_type = "List[Any]"  # Will be refined later
        elif schema_type == "object":
            base_type = "Dict[str, Any]"  # Will be refined later
        else:
            base_type = "Any"
        
        if is_array and not base_type.startswith("List"):
            self.imports.add("from typing import List")
            return f"List[{base_type}]"
        
        return base_type
    
    def get_default_value(self, field_info: Dict, is_required: bool, is_array: bool) -> str:
        """Generate appropriate default value for a field"""
        if is_required:
            return ""  # No default for required fields
        
        if is_array:
            return " = None"
        
        schema_type = field_info.get("type", "string")
        if schema_type == "string":
            return ' = None'
        elif schema_type in ["integer", "number"]:
            return ' = None'
        elif schema_type == "boolean":
            return ' = None'
        else:
            return ' = None'
    
    def extract_enum_values(self, field_schema: Dict) -> Optional[List[str]]:
        """Extract enum values from field schema"""
        if "enum" in field_schema:
            return field_schema["enum"]
        return None
    
    def process_object_schema(self, schema: Dict, class_name: str, parent_path: str = "") -> ClassInfo:
        """Process an object schema and extract class information"""
        properties = schema.get("properties", {})
        required_fields = set(schema.get("required", []))
        description = schema.get("description", f"Generated class for {class_name}")
        
        fields = []
        
        for field_name, field_schema in properties.items():
            python_field_name = self.sanitize_name(field_name)
            field_type = field_schema.get("type", "string")
            field_format = field_schema.get("format")
            field_description = field_schema.get("description", "")
            is_required = field_name in required_fields
            is_array = field_type == "array"
            
            # Handle array items
            if is_array:
                items_schema = field_schema.get("items", {})
                items_type = items_schema.get("type", "string")
                items_format = items_schema.get("format")
                enum_values = self.extract_enum_values(items_schema)
                
                if items_type == "object":
                    # Nested object in array
                    nested_class_name = self.sanitize_class_name(f"{class_name}_{field_name}_item")
                    nested_class = self.process_object_schema(items_schema, nested_class_name, 
                                                            f"{parent_path}.{field_name}")
                    self.classes[nested_class_name] = nested_class
                    item_type = nested_class_name
                else:
                    item_type = self.get_python_type(items_type, items_format, enum_values)
                
                type_hint = f"Optional[List[{item_type}]]"
                self.imports.add("from typing import List, Optional")
            else:
                enum_values = self.extract_enum_values(field_schema)
                
                if field_type == "object":
                    # Nested object
                    nested_class_name = self.sanitize_class_name(f"{class_name}_{field_name}")
                    nested_class = self.process_object_schema(field_schema, nested_class_name,
                                                            f"{parent_path}.{field_name}")
                    self.classes[nested_class_name] = nested_class
                    type_hint = nested_class_name
                else:
                    type_hint = self.get_python_type(field_type, field_format, enum_values)
                
                if not is_required:
                    type_hint = f"Optional[{type_hint}]"
                    self.imports.add("from typing import Optional")
            
            default_value = self.get_default_value(field_schema, is_required, is_array)
            
            field_info = FieldInfo(
                name=field_name,
                python_name=python_field_name,
                type_hint=type_hint,
                default_value=default_value,
                description=field_description,
                is_required=is_required,
                is_array=is_array,
                enum_values=enum_values,
                format_type=field_format
            )
            fields.append(field_info)
        
        return ClassInfo(
            name=class_name,
            python_name=self.sanitize_class_name(class_name),
            description=description,
            fields=fields,
            required_fields=required_fields,
            parent_path=parent_path
        )
    
    def analyze_schema(self):
        """Analyze the schema and extract all classes and enums"""
        print("🔍 Analyzing schema structure...")
        
        # Start with the root object
        if "properties" in self.schema:
            root_properties = self.schema["properties"]
            
            # Look for the main DMP object
            if "dmp" in root_properties:
                dmp_schema = root_properties["dmp"]
                main_class = self.process_object_schema(dmp_schema, "DMP", "dmp")
                self.classes["DMP"] = main_class
            else:
                # Process the entire schema as the main class
                main_class = self.process_object_schema(self.schema, "MainClass", "")
                self.classes["MainClass"] = main_class
        
        print(f"✅ Found {len(self.classes)} classes and {len(self.enums)} enums")
    
    def generate_enum_code(self, enum_name: str, enum_values: List[str]) -> str:
        """Generate Python enum code"""
        code = f"class {enum_name}(str, Enum):\n"
        code += f'    """Generated enum with {len(enum_values)} values"""\n'
        
        for value in enum_values:
            # Create valid Python identifier for enum member
            member_name = self.sanitize_name(value).upper()
            if member_name and not member_name[0].isdigit():
                code += f'    {member_name} = "{value}"\n'
            else:
                code += f'    VALUE_{hash(value) % 1000} = "{value}"\n'
        
        return code + "\n"

    def generate_class_code(self, class_info: ClassInfo) -> str:
        """Generate Python class code"""
        code = f"class {class_info.python_name}(BaseModel):\n"
        code += f'    """\n    {class_info.description}\n    """\n\n'

        # Generate fields
        for field in class_info.fields:
            if field.description:
                # Add field description as comment
                description_lines = field.description.replace('\n', ' ').strip()
                if len(description_lines) > 80:
                    description_lines = description_lines[:77] + "..."
                code += f"    # {description_lines}\n"

            code += f"    {field.python_name}: {field.type_hint}{field.default_value}\n"

        # Add validation methods if needed
        code += "\n"
        return code

    def generate_init_file(self) -> str:
        """Generate __init__.py file"""
        # Properly escape the schema file path
        schema_file_escaped = str(self.schema_file).replace('\\', '\\\\')

        code = f'"""\n{self.library_name} - Generated from JSON Schema\n\n'
        code += f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        code += f"Source schema: {schema_file_escaped}\n"
        code += '"""\n\n'

        # Import all classes
        code += "from .models import (\n"
        for class_name in sorted(self.classes.keys()):
            code += f"    {class_name},\n"
        code += ")\n\n"

        # Import enums
        if self.enums:
            code += "from .enums import (\n"
            for enum_name in sorted(self.enums.keys()):
                code += f"    {enum_name},\n"
            code += ")\n\n"

        code += f'__version__ = "1.0.0"\n'
        code += f'__all__ = {list(self.classes.keys()) + list(self.enums.keys())}\n'

        return code

    def generate_models_file(self) -> str:
        """Generate models.py file with all classes"""
        code = f'"""\nData models for {self.library_name}\n"""\n\n'

        # Add imports
        code += "from pydantic import BaseModel, Field, validator\n"
        code += "from typing import Optional, List, Dict, Any, Union\n"
        code += "from enum import Enum\n"

        # Add specific imports
        for import_stmt in sorted(self.imports):
            code += f"{import_stmt}\n"

        if self.enums:
            code += "from .enums import *\n"

        code += "\n\n"

        # Generate all classes
        for class_name in sorted(self.classes.keys()):
            class_info = self.classes[class_name]
            code += self.generate_class_code(class_info)
            code += "\n"

        return code

    def generate_enums_file(self) -> str:
        """Generate enums.py file"""
        if not self.enums:
            return ""

        code = f'"""\nEnumerations for {self.library_name}\n"""\n\n'
        code += "from enum import Enum\n\n"

        for enum_name, enum_values in self.enums.items():
            code += self.generate_enum_code(enum_name, enum_values)

        return code

    def generate_utils_file(self) -> str:
        """Generate utility functions"""
        code = f'"""\nUtility functions for {self.library_name}\n"""\n\n'
        code += "import json\nfrom typing import Dict, Any\nfrom .models import *\n\n"

        # Find the main class (usually DMP or the first class)
        main_class = "DMP" if "DMP" in self.classes else list(self.classes.keys())[0]

        code += f"""
def load_from_json(json_data: str) -> {main_class}:
    \"\"\"Load {main_class} from JSON string\"\"\"
    data = json.loads(json_data)
    return {main_class}(**data)

def load_from_file(file_path: str) -> {main_class}:
    \"\"\"Load {main_class} from JSON file\"\"\"
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return {main_class}(**data)

def export_to_json(instance: {main_class}, indent: int = 2) -> str:
    \"\"\"Export {main_class} instance to JSON string\"\"\"
    return instance.model_dump_json(indent=indent)

def export_to_file(instance: {main_class}, file_path: str, indent: int = 2):
    \"\"\"Export {main_class} instance to JSON file\"\"\"
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(export_to_json(instance, indent))

def validate_json(json_data: str) -> bool:
    \"\"\"Validate JSON data against the schema\"\"\"
    try:
        load_from_json(json_data)
        return True
    except Exception:
        return False
"""
        return code

    def generate_readme(self) -> str:
        """Generate README.md file"""
        main_class = "DMP" if "DMP" in self.classes else list(self.classes.keys())[0]

        readme = f"""# {self.library_name}

Auto-generated Python library from JSON Schema.

## Installation

```bash
pip install pydantic
```

## Usage

```python
import {self.library_name}

# Load from JSON file
data = {self.library_name}.load_from_file('data.json')

# Create new instance
{main_class.lower()} = {self.library_name}.{main_class}(
    # Add required fields here
)

# Export to JSON
json_output = {self.library_name}.export_to_json({main_class.lower()})

# Validate JSON
is_valid = {self.library_name}.validate_json(json_string)
```

## Generated Classes

This library contains {len(self.classes)} classes:

"""
        for class_name, class_info in sorted(self.classes.items()):
            readme += f"- **{class_name}**: {class_info.description[:100]}...\n"

        if self.enums:
            readme += f"\n## Enumerations\n\n{len(self.enums)} enumerations are available:\n\n"
            for enum_name, enum_values in self.enums.items():
                readme += f"- **{enum_name}**: {len(enum_values)} values\n"

        readme += f"""
## Schema Information

- **Source**: {self.schema_file}
- **Generated**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **Classes**: {len(self.classes)}
- **Enums**: {len(self.enums)}

## Requirements

- Python 3.8+
- pydantic >= 2.0
"""
        return readme

    def create_output_structure(self):
        """Create the output directory structure"""
        self.output_dir.mkdir(parents=True, exist_ok=True)
        (self.output_dir / self.library_name).mkdir(exist_ok=True)
        print(f"📁 Created output directory: {self.output_dir}")

    def write_files(self):
        """Write all generated files"""
        lib_dir = self.output_dir / self.library_name

        # Write __init__.py
        with open(lib_dir / "__init__.py", "w", encoding="utf-8") as f:
            f.write(self.generate_init_file())

        # Write models.py
        with open(lib_dir / "models.py", "w", encoding="utf-8") as f:
            f.write(self.generate_models_file())

        # Write enums.py (if needed)
        if self.enums:
            with open(lib_dir / "enums.py", "w", encoding="utf-8") as f:
                f.write(self.generate_enums_file())

        # Write utils.py
        with open(lib_dir / "utils.py", "w", encoding="utf-8") as f:
            f.write(self.generate_utils_file())

        # Write README.md
        with open(self.output_dir / "README.md", "w", encoding="utf-8") as f:
            f.write(self.generate_readme())

        # Write requirements.txt
        with open(self.output_dir / "requirements.txt", "w", encoding="utf-8") as f:
            f.write("pydantic>=2.0.0\n")

        print(f"✅ Generated library files in {lib_dir}")

    def generate(self):
        """Main generation method"""
        print(f"🚀 Generating Python library '{self.library_name}' from schema...")

        self.load_schema()
        self.analyze_schema()
        self.create_output_structure()
        self.write_files()

        print(f"🎉 Successfully generated library!")
        print(f"📍 Location: {self.output_dir}")
        print(f"📦 Library name: {self.library_name}")
        print(f"🏗️  Classes: {len(self.classes)}")
        print(f"🔢 Enums: {len(self.enums)}")


def main():
    """Main entry point"""
    if len(sys.argv) < 3:
        print("Usage: python schema_to_library_generator.py <schema_file> <output_directory> [library_name]")
        print("\nExample:")
        print("  python schema_to_library_generator.py data/schema.json ./generated my_lib")
        sys.exit(1)

    schema_file = sys.argv[1]
    output_dir = sys.argv[2]
    library_name = sys.argv[3] if len(sys.argv) > 3 else "generated_lib"

    if not os.path.exists(schema_file):
        print(f"❌ Schema file not found: {schema_file}")
        sys.exit(1)

    generator = SchemaToLibraryGenerator(schema_file, output_dir, library_name)
    generator.generate()


if __name__ == "__main__":
    main()
