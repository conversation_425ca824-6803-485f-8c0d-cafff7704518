# DMP Generator Web Application - Deployment Guide

This guide explains how to deploy a web application that allows users to create Data Management Plans (DMPs) using the madmpy library.

## 🎯 What You Get

A complete web application with:
- **User-friendly forms** for DMP creation
- **Real-time validation** using madmpy
- **JSON export** functionality
- **Example DMP browser**
- **File upload validation**
- **Responsive design** that works on mobile and desktop

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)

**For Windows:**
```cmd
run_webapp.bat
```

**For Linux/Mac:**
```bash
python run_webapp.py
```

### Option 2: Manual Setup

1. **Install dependencies:**
   ```bash
   pip install -r webapp_requirements.txt
   pip install -e .
   ```

2. **Run the application:**
   ```bash
   cd webapp
   python app.py
   ```

3. **Access the app:**
   Open http://localhost:5000 in your browser

## 📁 Project Structure

```
madmpy/
├── webapp/                     # Web application files
│   ├── app.py                 # Main Flask application
│   ├── templates/             # HTML templates
│   │   ├── base.html         # Base layout
│   │   ├── index.html        # Home page
│   │   ├── create_dmp.html   # DMP creation form
│   │   └── examples.html     # Examples browser
│   └── README.md             # Webapp documentation
├── webapp_requirements.txt    # Web app dependencies
├── run_webapp.py             # Cross-platform launcher
├── run_webapp.bat            # Windows launcher
└── WEBAPP_DEPLOYMENT_GUIDE.md # This file
```

## 🌐 Application Features

### 1. Home Page (`/`)
- Overview of available features
- Quick file validation
- Navigation to other sections

### 2. DMP Creation (`/create_dmp`)
- **Basic Information**: Title, description, language
- **Contact Details**: Name, email, ORCID ID
- **Multiple Datasets**: Add/remove datasets dynamically
  - Title, description, data sensitivity per dataset
  - Technical resources per dataset
  - Individual dataset identifiers (DOI/URL)
- **Ethical Considerations**: Ethics review requirements
- **Example Data Loading**: Pre-fill form with sample data (2 datasets)
- **Real-time Validation**: Immediate feedback on form errors
- **JSON Export**: Download generated DMP

### 3. Examples Browser (`/examples`)
- View all example DMPs from the `data/` directory
- Detailed view with syntax highlighting
- Download individual examples
- Copy JSON to clipboard

### 4. File Validation
- Upload existing DMP JSON files
- Validate against RDA-DMP Common Standard
- Display validation results with error details

## 🔧 Configuration

### Environment Variables
```bash
export FLASK_ENV=development    # Enable debug mode
export SECRET_KEY=your-secret   # Change in production
```

### Production Deployment

**Using Gunicorn (Linux/Mac):**
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 webapp.app:app
```

**Using Waitress (Windows):**
```bash
pip install waitress
waitress-serve --host=0.0.0.0 --port=5000 webapp.app:app
```

### Docker Deployment

Create `Dockerfile`:
```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY . .

RUN pip install -r webapp_requirements.txt
RUN pip install -e .

EXPOSE 5000
CMD ["python", "webapp/app.py"]
```

Build and run:
```bash
docker build -t dmp-generator .
docker run -p 5000:5000 dmp-generator
```

## 🎨 Customization

### Styling
- Edit `webapp/templates/base.html` for global styles
- Bootstrap 5 is included via CDN
- Font Awesome icons available

### Form Fields
- Modify `webapp/app.py` to add/remove form fields
- Update corresponding templates
- Adjust madmpy object creation logic

### Validation Rules
- Custom validation in `webapp/app.py`
- WTForms validators for client-side validation
- madmpy handles DMP standard compliance

## 🔍 API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/` | Home page |
| GET/POST | `/create_dmp` | DMP creation form |
| POST | `/download_dmp` | Download DMP as JSON |
| POST | `/validate_dmp` | Validate uploaded DMP |
| GET | `/examples` | Browse example DMPs |

## 🐛 Troubleshooting

### Common Issues

**1. Import Error: No module named 'madmpy'**
```bash
# Solution: Install madmpy in development mode
pip install -e .
```

**2. Template Not Found**
```bash
# Solution: Run from correct directory
cd webapp
python app.py
```

**3. Port Already in Use**
```bash
# Solution: Use different port
python app.py --port 8000
```

**4. Form Validation Errors**
- Check required fields are filled
- Ensure email format is valid
- Verify URLs include http:// or https://

### Debug Mode
```bash
export FLASK_ENV=development
cd webapp
python app.py
```

## 🔒 Security Considerations

### For Production:
1. **Change Secret Key**: Update `SECRET_KEY` in `app.py`
2. **Disable Debug**: Set `debug=False`
3. **Use HTTPS**: Configure SSL/TLS
4. **Input Validation**: Already implemented via WTForms
5. **File Upload Limits**: Consider adding file size limits

### Example Production Config:
```python
import os
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'fallback-key')
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB limit
```

## 📊 Usage Analytics

To track usage, consider adding:
- Google Analytics
- Application logging
- User session tracking
- DMP creation metrics

## 🤝 Contributing

1. Fork the repository
2. Create feature branch: `git checkout -b feature/new-feature`
3. Test your changes
4. Submit pull request

## 📞 Support

- **Documentation**: Check `webapp/README.md`
- **Issues**: Open GitHub issue
- **Email**: Contact maintainers

## 🎉 Success!

Your DMP Generator web application is now ready! Users can:
- ✅ Create DMPs through an intuitive web interface
- ✅ Support multiple datasets in a single DMP
- ✅ Dynamic add/remove datasets functionality
- ✅ Validate existing DMP files
- ✅ Download results as JSON
- ✅ Browse and learn from examples
- ✅ Access the app from any device with a web browser

The application follows the RDA-DMP Common Standard and integrates seamlessly with the madmpy library.
