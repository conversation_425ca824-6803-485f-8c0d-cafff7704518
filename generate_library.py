#!/usr/bin/env python3
"""
Automated Library Generation Script

This script provides a simple interface for generating Python libraries from JSON schemas.
It handles both basic and advanced generation with proper error handling and validation.

Usage:
    python generate_library.py --schema <schema_file> --output <output_dir> --name <library_name> [--advanced]
"""

import argparse
import os
import sys
import subprocess
import json
import time
from pathlib import Path
from datetime import datetime


class LibraryGenerator:
    """Main library generator class"""
    
    def __init__(self, schema_file: str, output_dir: str, library_name: str, advanced: bool = False):
        self.schema_file = Path(schema_file)
        self.output_dir = Path(output_dir)
        self.library_name = library_name
        self.advanced = advanced
        self.generator_script = "advanced_schema_generator.py" if advanced else "schema_to_library_generator.py"
    
    def validate_inputs(self) -> bool:
        """Validate input parameters"""
        print("🔍 Validating inputs...")
        
        # Check schema file exists
        if not self.schema_file.exists():
            print(f"❌ Schema file not found: {self.schema_file}")
            return False
        
        # Check schema is valid JSON
        try:
            with open(self.schema_file, 'r', encoding='utf-8') as f:
                json.load(f)
            print(f"✅ Schema file is valid JSON: {self.schema_file}")
        except json.JSONDecodeError as e:
            print(f"❌ Invalid JSON in schema file: {e}")
            return False
        
        # Check generator script exists
        if not Path(self.generator_script).exists():
            print(f"❌ Generator script not found: {self.generator_script}")
            return False
        
        # Validate library name
        if not self.library_name.isidentifier():
            print(f"❌ Invalid library name: {self.library_name} (must be valid Python identifier)")
            return False
        
        print("✅ All inputs validated successfully")
        return True
    
    def analyze_schema(self) -> dict:
        """Analyze schema complexity"""
        print("📊 Analyzing schema complexity...")
        
        with open(self.schema_file, 'r', encoding='utf-8') as f:
            schema = json.load(f)
        
        def count_elements(obj, counts=None):
            if counts is None:
                counts = {"objects": 0, "arrays": 0, "enums": 0, "properties": 0}
            
            if isinstance(obj, dict):
                if obj.get("type") == "object":
                    counts["objects"] += 1
                elif obj.get("type") == "array":
                    counts["arrays"] += 1
                
                if "enum" in obj:
                    counts["enums"] += 1
                
                if "properties" in obj:
                    counts["properties"] += len(obj["properties"])
                
                for value in obj.values():
                    count_elements(value, counts)
            
            elif isinstance(obj, list):
                for item in obj:
                    count_elements(item, counts)
            
            return counts
        
        complexity = count_elements(schema)
        file_size = self.schema_file.stat().st_size
        
        analysis = {
            "file_size": file_size,
            "objects": complexity["objects"],
            "arrays": complexity["arrays"],
            "enums": complexity["enums"],
            "properties": complexity["properties"],
            "estimated_classes": max(complexity["objects"], 1),
            "complexity_score": (complexity["objects"] + complexity["arrays"] + complexity["enums"]) / 10
        }
        
        print(f"   📄 File size: {file_size:,} bytes")
        print(f"   🏗️  Objects: {complexity['objects']}")
        print(f"   📋 Arrays: {complexity['arrays']}")
        print(f"   🔢 Enums: {complexity['enums']}")
        print(f"   📝 Properties: {complexity['properties']}")
        print(f"   🎯 Estimated classes: {analysis['estimated_classes']}")
        
        if analysis["complexity_score"] > 10:
            print("   ⚠️  High complexity schema - consider using advanced generator")
        
        return analysis
    
    def estimate_generation_time(self, analysis: dict) -> int:
        """Estimate generation time in seconds"""
        base_time = 5  # Base time in seconds
        complexity_factor = analysis["complexity_score"]
        file_size_factor = analysis["file_size"] / (1024 * 1024)  # MB
        
        estimated_time = base_time + (complexity_factor * 2) + (file_size_factor * 3)
        
        if self.advanced:
            estimated_time *= 1.5  # Advanced generator takes longer
        
        return int(estimated_time)
    
    def generate_library(self) -> bool:
        """Generate the library"""
        print(f"🚀 Generating {'advanced' if self.advanced else 'basic'} library...")
        print(f"   📁 Output: {self.output_dir}")
        print(f"   📦 Name: {self.library_name}")
        
        # Prepare command
        cmd = [
            sys.executable,
            self.generator_script,
            str(self.schema_file),
            str(self.output_dir),
            self.library_name
        ]
        
        start_time = time.time()
        
        try:
            # Run generator with timeout
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            generation_time = time.time() - start_time
            
            if result.returncode == 0:
                print(f"✅ Library generated successfully in {generation_time:.1f} seconds")
                if result.stdout:
                    print("📝 Generator output:")
                    print(result.stdout)
                return True
            else:
                print(f"❌ Generation failed (exit code: {result.returncode})")
                if result.stderr:
                    print("🔥 Error output:")
                    print(result.stderr)
                return False
        
        except subprocess.TimeoutExpired:
            print("❌ Generation timed out (5 minutes)")
            return False
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            return False
    
    def validate_generated_library(self) -> bool:
        """Validate the generated library"""
        print("🧪 Validating generated library...")
        
        library_path = self.output_dir / self.library_name
        
        # Check if library directory exists
        if not library_path.exists():
            print(f"❌ Library directory not found: {library_path}")
            return False
        
        # Check required files
        required_files = ["__init__.py", "models.py", "utils.py"]
        missing_files = []
        
        for file_name in required_files:
            file_path = library_path / file_name
            if not file_path.exists():
                missing_files.append(file_name)
        
        if missing_files:
            print(f"❌ Missing required files: {missing_files}")
            return False
        
        # Try to import the library
        try:
            sys.path.insert(0, str(self.output_dir))
            lib = __import__(self.library_name)
            
            # Check for main classes and functions
            checks = {
                "DMP class": hasattr(lib, 'DMP'),
                "load_from_json": hasattr(lib, 'load_from_json'),
                "export_to_json": hasattr(lib, 'export_to_json'),
                "validate_json": hasattr(lib, 'validate_json')
            }
            
            passed_checks = sum(checks.values())
            total_checks = len(checks)
            
            print(f"✅ Library validation: {passed_checks}/{total_checks} checks passed")
            
            for check_name, passed in checks.items():
                status = "✅" if passed else "⚠️"
                print(f"   {status} {check_name}")
            
            return passed_checks >= total_checks // 2  # At least half should pass
        
        except ImportError as e:
            print(f"❌ Import error: {e}")
            return False
        except Exception as e:
            print(f"❌ Validation error: {e}")
            return False
    
    def create_usage_example(self):
        """Create a usage example file"""
        example_file = self.output_dir / f"example_usage_{self.library_name}.py"
        
        example_code = f'''#!/usr/bin/env python3
"""
Example usage of the generated {self.library_name} library

Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Source schema: {self.schema_file}
"""

import {self.library_name}

def main():
    """Example usage of the library"""
    
    # Example 1: Load from JSON file
    try:
        # Replace 'data.json' with your actual data file
        # data = {self.library_name}.load_from_file('data.json')
        # print("✅ Successfully loaded data from file")
        pass
    except FileNotFoundError:
        print("⚠️  No data file found - create one to test loading")
    except Exception as e:
        print(f"❌ Error loading from file: {{e}}")
    
    # Example 2: Create new instance
    try:
        # Create a new DMP instance (adjust fields as needed)
        # dmp = {self.library_name}.DMP(
        #     # Add required fields here based on your schema
        # )
        # print("✅ Successfully created new DMP instance")
        pass
    except Exception as e:
        print(f"❌ Error creating instance: {{e}}")
    
    # Example 3: Validate JSON string
    sample_json = '{{"test": "data"}}'
    is_valid = {self.library_name}.validate_json(sample_json)
    print(f"📝 JSON validation result: {{is_valid}}")
    
    # Example 4: Export to JSON
    try:
        # json_output = {self.library_name}.export_to_json(dmp)
        # print("✅ Successfully exported to JSON")
        pass
    except Exception as e:
        print(f"❌ Error exporting to JSON: {{e}}")

if __name__ == "__main__":
    main()
'''
        
        with open(example_file, 'w', encoding='utf-8') as f:
            f.write(example_code)
        
        print(f"📝 Created usage example: {example_file}")
    
    def run(self) -> bool:
        """Run the complete generation process"""
        print("🎯 Starting Library Generation Process")
        print("=" * 50)
        
        # Step 1: Validate inputs
        if not self.validate_inputs():
            return False
        
        # Step 2: Analyze schema
        analysis = self.analyze_schema()
        
        # Step 3: Estimate time
        estimated_time = self.estimate_generation_time(analysis)
        print(f"⏱️  Estimated generation time: {estimated_time} seconds")
        
        # Step 4: Generate library
        if not self.generate_library():
            return False
        
        # Step 5: Validate generated library
        if not self.validate_generated_library():
            print("⚠️  Library validation failed, but files were generated")
        
        # Step 6: Create usage example
        self.create_usage_example()
        
        # Step 7: Final summary
        print("\n🎉 Generation Complete!")
        print(f"📁 Library location: {self.output_dir / self.library_name}")
        print(f"📖 Documentation: {self.output_dir / 'README.md'}")
        print(f"🧪 Tests: {self.output_dir / 'test_library.py'}")
        print(f"💡 Example: {self.output_dir / f'example_usage_{self.library_name}.py'}")
        
        return True


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Generate Python library from JSON schema",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic generation
  python generate_library.py --schema data/schema.json --output ./my_lib --name my_library
  
  # Advanced generation with validation
  python generate_library.py --schema data/complex_schema.json --output ./advanced_lib --name advanced_library --advanced
  
  # GCWG schema example
  python generate_library.py --schema data/GCWG-RDA-maDMP-schema.json --output ./gcwg --name gcwg_madmp --advanced
        """
    )
    
    parser.add_argument("--schema", "-s", required=True, help="Path to JSON schema file")
    parser.add_argument("--output", "-o", required=True, help="Output directory for generated library")
    parser.add_argument("--name", "-n", required=True, help="Name of the generated library")
    parser.add_argument("--advanced", "-a", action="store_true", help="Use advanced generator with validation")
    
    args = parser.parse_args()
    
    # Create generator and run
    generator = LibraryGenerator(args.schema, args.output, args.name, args.advanced)
    success = generator.run()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
