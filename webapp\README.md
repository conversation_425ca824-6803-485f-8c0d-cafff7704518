# DMP Generator Web Application

A Flask-based web application for creating and validating Data Management Plans (DMPs) using the madmpy library.

## Features

- **Create DMPs**: User-friendly web form to create new DMPs following the RDA-DMP Common Standard
- **Validate DMPs**: Upload and validate existing DMP JSON files
- **View Examples**: Browse example DMPs from the data directory
- **Download Results**: Export generated DMPs as JSON files
- **Responsive Design**: Mobile-friendly interface using Bootstrap

## Installation

### Prerequisites

- Python 3.11 or higher
- pip package manager

### Setup

1. **Clone the repository** (if not already done):
   ```bash
   git clone https://github.com/msicilia/madmpy.git
   cd madmpy
   ```

2. **Install dependencies**:
   ```bash
   pip install -r webapp_requirements.txt
   ```

3. **Install madmpy in development mode**:
   ```bash
   pip install -e .
   ```

## Running the Application

### Development Mode

1. **Navigate to the webapp directory**:
   ```bash
   cd webapp
   ```

2. **Run the Flask application**:
   ```bash
   python app.py
   ```

3. **Access the application**:
   Open your web browser and go to: `http://localhost:5000`

### Production Deployment

For production deployment, consider using a WSGI server like Gunicorn:

1. **Install Gunicorn**:
   ```bash
   pip install gunicorn
   ```

2. **Run with Gunicorn**:
   ```bash
   gunicorn -w 4 -b 0.0.0.0:5000 app:app
   ```

## Usage

### Creating a New DMP

1. Click "Create DMP" from the home page
2. Fill out the form with your project information:
   - **Basic Information**: DMP title, description, language
   - **Contact Information**: Name, email, ORCID ID
   - **Dataset Information**: Dataset details, data sensitivity
   - **Ethical Considerations**: Ethical issues and descriptions
3. Click "Create DMP" to generate the DMP
4. Download the resulting JSON file

### Validating an Existing DMP

1. From the home page, use the "Validate Existing DMP" section
2. Upload a JSON file containing a DMP
3. The system will validate it against the RDA-DMP Common Standard
4. View validation results

### Viewing Examples

1. Click "Examples" to see sample DMPs
2. View details of any example
3. Download example files for reference

## Configuration

### Environment Variables

- `FLASK_ENV`: Set to `development` for development mode
- `SECRET_KEY`: Change the secret key in production (set in `app.py`)

### Customization

- **Templates**: Modify HTML templates in `templates/` directory
- **Styling**: Update CSS in the `base.html` template
- **Form Fields**: Modify form fields in `app.py` and corresponding templates

## API Endpoints

- `GET /`: Home page
- `GET,POST /create_dmp`: DMP creation form and processing
- `POST /download_dmp`: Download generated DMP as JSON
- `POST /validate_dmp`: Validate uploaded DMP file
- `GET /examples`: View example DMPs

## File Structure

```
webapp/
├── app.py                 # Main Flask application
├── templates/             # HTML templates
│   ├── base.html         # Base template with common layout
│   ├── index.html        # Home page
│   ├── create_dmp.html   # DMP creation form
│   └── examples.html     # Examples page
└── README.md             # This file
```

## Dependencies

- Flask: Web framework
- WTForms: Form handling and validation
- Flask-WTF: Flask integration for WTForms
- madmpy: Core DMP library (from parent directory)
- Bootstrap 5: Frontend styling (CDN)
- Font Awesome: Icons (CDN)

## Troubleshooting

### Common Issues

1. **Import Error for madmpy**:
   - Ensure you've installed madmpy: `pip install -e .` from the root directory
   - Check that the Python path includes the src directory

2. **Template Not Found**:
   - Ensure you're running the app from the webapp directory
   - Check that templates directory exists and contains the required files

3. **Form Validation Errors**:
   - Check that all required fields are filled
   - Ensure email addresses and URLs are in valid format

### Debug Mode

To enable debug mode, set the Flask environment:
```bash
export FLASK_ENV=development  # Linux/Mac
set FLASK_ENV=development     # Windows
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test the application
5. Submit a pull request

## License

This project follows the same license as the parent madmpy project (MIT License).
