#!/usr/bin/env python3
"""
Complete Schema to Web Application Pipeline

This script combines the schema generator and dynamic form generator to create
a complete web application directly from a JSON schema in one command.

Usage:
    python schema_to_webapp.py --schema <schema_file> --output <webapp_dir> --name <app_name> [--advanced]

Example:
    python schema_to_webapp.py --schema data/GCWG-RDA-maDMP-schema.json --output ./gcwg_webapp --name "GCWG DMP Generator" --advanced
"""

import argparse
import os
import sys
import subprocess
import tempfile
import shutil
from pathlib import Path
from datetime import datetime


class SchemaToWebAppPipeline:
    """Complete pipeline from schema to web application"""
    
    def __init__(self, schema_file: str, output_dir: str, app_name: str, advanced: bool = False):
        self.schema_file = Path(schema_file)
        self.output_dir = Path(output_dir)
        self.app_name = app_name
        self.advanced = advanced
        self.temp_lib_dir = None
        self.library_name = None
    
    def validate_inputs(self) -> bool:
        """Validate input parameters"""
        print("🔍 Validating inputs...")
        
        if not self.schema_file.exists():
            print(f"❌ Schema file not found: {self.schema_file}")
            return False
        
        # Test if schema is valid JSON
        try:
            import json
            with open(self.schema_file, 'r', encoding='utf-8') as f:
                json.load(f)
            print(f"✅ Schema file is valid JSON: {self.schema_file}")
        except json.JSONDecodeError as e:
            print(f"❌ Invalid JSON in schema file: {e}")
            return False
        
        # Check if generator scripts exist
        schema_generator = "advanced_schema_generator.py" if self.advanced else "schema_to_library_generator.py"
        form_generator = "dynamic_form_generator.py"
        
        if not Path(schema_generator).exists():
            print(f"❌ Schema generator not found: {schema_generator}")
            return False
        
        if not Path(form_generator).exists():
            print(f"❌ Form generator not found: {form_generator}")
            return False
        
        print("✅ All inputs validated successfully")
        return True
    
    def generate_library_name(self) -> str:
        """Generate a library name from schema file"""
        base_name = self.schema_file.stem
        # Clean up the name
        clean_name = base_name.lower().replace('-', '_').replace(' ', '_')
        # Remove common suffixes
        for suffix in ['_schema', '_json', '_spec']:
            if clean_name.endswith(suffix):
                clean_name = clean_name[:-len(suffix)]
        
        return clean_name or "generated_lib"
    
    def step1_generate_library(self) -> bool:
        """Step 1: Generate Python library from schema"""
        print("📚 Step 1: Generating Python library from schema...")
        
        # Create temporary directory for library
        self.temp_lib_dir = Path(tempfile.mkdtemp(prefix="schema_lib_"))
        self.library_name = self.generate_library_name()
        
        print(f"   📁 Temporary library directory: {self.temp_lib_dir}")
        print(f"   📦 Library name: {self.library_name}")
        
        # Choose generator
        generator_script = "advanced_schema_generator.py" if self.advanced else "schema_to_library_generator.py"
        generator_type = "Advanced" if self.advanced else "Basic"
        
        print(f"   🔧 Using {generator_type} generator")
        
        # Run the schema generator
        cmd = [
            sys.executable,
            generator_script,
            str(self.schema_file),
            str(self.temp_lib_dir),
            self.library_name
        ]
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minutes
            )
            
            if result.returncode == 0:
                print("   ✅ Library generated successfully")
                if result.stdout:
                    # Print key info from generator output
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if 'classes' in line.lower() or 'enums' in line.lower():
                            print(f"   📊 {line.strip()}")
                return True
            else:
                print(f"   ❌ Library generation failed: {result.stderr}")
                return False
        
        except subprocess.TimeoutExpired:
            print("   ❌ Library generation timed out (5 minutes)")
            return False
        except Exception as e:
            print(f"   ❌ Error generating library: {e}")
            return False
    
    def step2_generate_webapp(self) -> bool:
        """Step 2: Generate web application from library"""
        print("🎨 Step 2: Generating web application from library...")
        
        library_path = self.temp_lib_dir / self.library_name
        
        if not library_path.exists():
            print(f"   ❌ Library not found at: {library_path}")
            return False
        
        print(f"   📁 Library path: {library_path}")
        print(f"   🌐 Web app output: {self.output_dir}")
        print(f"   🏷️  App name: {self.app_name}")
        
        # Run the dynamic form generator
        cmd = [
            sys.executable,
            "dynamic_form_generator.py",
            "--library", str(library_path),
            "--output", str(self.output_dir),
            "--app-name", self.app_name
        ]
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=180  # 3 minutes
            )
            
            if result.returncode == 0:
                print("   ✅ Web application generated successfully")
                return True
            else:
                print(f"   ❌ Web app generation failed: {result.stderr}")
                return False
        
        except subprocess.TimeoutExpired:
            print("   ❌ Web app generation timed out (3 minutes)")
            return False
        except Exception as e:
            print(f"   ❌ Error generating web app: {e}")
            return False
    
    def step3_finalize(self) -> bool:
        """Step 3: Finalize and clean up"""
        print("🎯 Step 3: Finalizing web application...")
        
        # Check if webapp was created successfully
        required_files = [
            "app.py",
            "templates/index.html",
            "templates/create_form.html",
            "static/js/dynamic-forms.js",
            "requirements.txt"
        ]
        
        missing_files = []
        for file_path in required_files:
            if not (self.output_dir / file_path).exists():
                missing_files.append(file_path)
        
        if missing_files:
            print(f"   ❌ Missing required files: {missing_files}")
            return False
        
        # Create a startup script
        self.create_startup_script()
        
        # Create deployment guide
        self.create_deployment_guide()
        
        print("   ✅ Web application finalized")
        return True
    
    def create_startup_script(self):
        """Create a startup script for the webapp"""
        startup_script = self.output_dir / "start_webapp.py"
        
        script_content = f'''#!/usr/bin/env python3
"""
Startup script for {self.app_name}

Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Source schema: {self.schema_file}
"""

import subprocess
import sys
import os
from pathlib import Path

def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {{e}}")
        return False

def start_webapp():
    """Start the web application"""
    print("🚀 Starting {self.app_name}...")
    print("📍 Application will be available at: http://localhost:5000")
    print("🛑 Press Ctrl+C to stop the server")
    print("-" * 50)
    
    try:
        subprocess.run([sys.executable, "app.py"])
    except KeyboardInterrupt:
        print("\\n👋 Application stopped by user")
    except Exception as e:
        print(f"❌ Error running application: {{e}}")

def main():
    """Main function"""
    print("🎯 {self.app_name} - Startup")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("app.py").exists():
        print("❌ app.py not found. Make sure you're in the webapp directory.")
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        sys.exit(1)
    
    # Start the webapp
    start_webapp()

if __name__ == "__main__":
    main()
'''
        
        with open(startup_script, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        # Make it executable on Unix systems
        try:
            os.chmod(startup_script, 0o755)
        except:
            pass
        
        print(f"   📝 Created startup script: {startup_script}")
    
    def create_deployment_guide(self):
        """Create a deployment guide"""
        guide_file = self.output_dir / "DEPLOYMENT.md"
        
        guide_content = f'''# {self.app_name} - Deployment Guide

Auto-generated web application from JSON schema.

## Quick Start

### Option 1: Use the Startup Script
```bash
python start_webapp.py
```

### Option 2: Manual Start
```bash
pip install -r requirements.txt
python app.py
```

Then open: http://localhost:5000

## Generated Information

- **Source Schema**: {self.schema_file}
- **Library Name**: {self.library_name}
- **Generator Type**: {"Advanced" if self.advanced else "Basic"}
- **Generated On**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Features

- ✅ Dynamic form generation from library structure
- ✅ Automatic validation based on schema constraints
- ✅ JSON export and download functionality
- ✅ File upload and validation
- ✅ Responsive web interface
- ✅ API endpoints for programmatic access

## Production Deployment

### Using Gunicorn
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### Using Docker
```dockerfile
FROM python:3.11-slim
COPY . /app
WORKDIR /app
RUN pip install -r requirements.txt
EXPOSE 5000
CMD ["python", "app.py"]
```

### Environment Variables
- `FLASK_ENV`: Set to `production` for production deployment
- `SECRET_KEY`: Change the secret key in app.py for production

## Updating

When your schema changes:

1. Regenerate the webapp:
   ```bash
   python schema_to_webapp.py --schema {self.schema_file} --output {self.output_dir} --name "{self.app_name}" {"--advanced" if self.advanced else ""}
   ```

2. Restart the application

## Support

This application was automatically generated. For issues:
1. Check that the source schema is valid
2. Verify all dependencies are installed
3. Regenerate if the schema has been updated
'''
        
        with open(guide_file, 'w', encoding='utf-8') as f:
            f.write(guide_content)
        
        print(f"   📖 Created deployment guide: {guide_file}")
    
    def cleanup(self):
        """Clean up temporary files"""
        if self.temp_lib_dir and self.temp_lib_dir.exists():
            try:
                shutil.rmtree(self.temp_lib_dir)
                print(f"🧹 Cleaned up temporary directory: {self.temp_lib_dir}")
            except Exception as e:
                print(f"⚠️  Could not clean up {self.temp_lib_dir}: {e}")
    
    def run_pipeline(self) -> bool:
        """Run the complete pipeline"""
        print("🚀 Schema to Web Application Pipeline")
        print("=" * 50)
        print(f"📄 Schema: {self.schema_file}")
        print(f"🌐 Output: {self.output_dir}")
        print(f"🏷️  App Name: {self.app_name}")
        print(f"🔧 Generator: {'Advanced' if self.advanced else 'Basic'}")
        print()
        
        try:
            # Validate inputs
            if not self.validate_inputs():
                return False
            
            # Step 1: Generate library
            if not self.step1_generate_library():
                return False
            
            # Step 2: Generate webapp
            if not self.step2_generate_webapp():
                return False
            
            # Step 3: Finalize
            if not self.step3_finalize():
                return False
            
            # Success!
            print("\n🎉 Pipeline completed successfully!")
            print(f"📁 Web application created: {self.output_dir}")
            print(f"🚀 To start: cd {self.output_dir} && python start_webapp.py")
            print(f"🌐 Then open: http://localhost:5000")
            
            return True
        
        finally:
            # Always clean up
            self.cleanup()


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Complete pipeline: JSON Schema → Python Library → Web Application",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # GCWG schema to webapp
  python schema_to_webapp.py --schema data/GCWG-RDA-maDMP-schema.json --output ./gcwg_webapp --name "GCWG DMP Generator" --advanced
  
  # Simple schema to webapp
  python schema_to_webapp.py --schema my_schema.json --output ./my_webapp --name "My Data Manager"
  
  # Custom research schema
  python schema_to_webapp.py --schema research.json --output ./research_app --name "Research Data Portal" --advanced
        """
    )
    
    parser.add_argument("--schema", "-s", required=True, help="Path to JSON schema file")
    parser.add_argument("--output", "-o", required=True, help="Output directory for web application")
    parser.add_argument("--name", "-n", required=True, help="Name of the web application")
    parser.add_argument("--advanced", "-a", action="store_true", help="Use advanced generator with enhanced validation")
    
    args = parser.parse_args()
    
    # Create and run pipeline
    pipeline = SchemaToWebAppPipeline(args.schema, args.output, args.name, args.advanced)
    success = pipeline.run_pipeline()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
