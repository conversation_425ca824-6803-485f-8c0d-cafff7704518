#!/usr/bin/env python3
"""
Test script to verify multiple datasets functionality in madmpy
"""
import sys
import os
import datetime

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
import madmpy

def test_multiple_datasets():
    """Test creating a DMP with multiple datasets"""
    print("Testing multiple datasets functionality...")
    
    # Load madmpy
    dmp_module = madmpy.load()
    
    # Create contact
    contact = dmp_module.Contact(
        name="Dr. <PERSON>",
        contact_id=dmp_module.ContactIdentifier(
            identifier="https://orcid.org/0000-0001-2345-6789",
            type=dmp_module.contact_id_type.ORCID,
        ),
        mbox="<EMAIL>",
    )
    
    # Create multiple datasets
    dataset1 = dmp_module.Dataset(
        dataset_id=dmp_module.DatasetIdentifier(
            identifier="https://doi.org/10.25504/FAIRsharing.r3vtvx",
            type=dmp_module.dmp_dataset_id_type.DOI,
        ),
        description="Climate data including temperature and precipitation measurements",
        personal_data="no",
        sensitive_data="no",
        technical_resource=[dmp_module.TechnicalResource(name="Weather Station Network")],
        title="Climate Data Collection 2024",
    )
    
    dataset2 = dmp_module.Dataset(
        dataset_id=dmp_module.DatasetIdentifier(
            identifier="https://doi.org/10.25504/FAIRsharing.soil123",
            type=dmp_module.dmp_dataset_id_type.DOI,
        ),
        description="Chemical composition and pH levels of soil samples",
        personal_data="no",
        sensitive_data="no",
        technical_resource=[dmp_module.TechnicalResource(name="Laboratory Equipment")],
        title="Soil Analysis Dataset",
    )
    
    dataset3 = dmp_module.Dataset(
        dataset_id=dmp_module.DatasetIdentifier(
            identifier="https://doi.org/10.25504/FAIRsharing.bio456",
            type=dmp_module.dmp_dataset_id_type.DOI,
        ),
        description="Biodiversity survey data from various ecosystems",
        personal_data="no",
        sensitive_data="no",
        technical_resource=[dmp_module.TechnicalResource(name="Field Survey Equipment")],
        title="Biodiversity Survey Data",
    )
    
    # Create DMP with multiple datasets
    dmp_id = dmp_module.DMPIdentifier(
        identifier="https://doi.org/10.15497/rda00039",
        type=dmp_module.dmp_dataset_id_type.DOI
    )
    
    dmp = dmp_module.DMP(
        dataset=[dataset1, dataset2, dataset3],  # Multiple datasets!
        language=dmp_module.LanguageEnum.eng,
        title="Multi-Dataset Research Project DMP",
        contact=contact,
        dmp_id=dmp_id,
        ethical_issues_exist=dmp_module.YesNoUnknown.NO,
        created=datetime.datetime.now().replace(microsecond=0),
        modified=datetime.datetime.now().replace(microsecond=0),
        description="A comprehensive DMP covering multiple datasets for environmental research",
    )
    
    # Export to JSON
    dmp_json = madmpy.export_DMP_json(dmp)
    
    print(f"✅ Successfully created DMP with {len(dmp.dataset)} datasets!")
    print(f"📊 Dataset titles:")
    for i, dataset in enumerate(dmp.dataset, 1):
        print(f"   {i}. {dataset.title}")
    
    print(f"\n📄 JSON structure preview:")
    print(f"   - DMP Title: {dmp_json['dmp']['title']}")
    print(f"   - Number of datasets: {len(dmp_json['dmp']['dataset'])}")
    print(f"   - Contact: {dmp_json['dmp']['contact']['name']}")
    
    # Validate the DMP
    try:
        dmp_module.DMP.model_validate(dmp)
        print("✅ DMP validation successful!")
    except Exception as e:
        print(f"❌ DMP validation failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = test_multiple_datasets()
    if success:
        print("\n🎉 Multiple datasets functionality is working correctly!")
        print("The web application should now support creating DMPs with multiple datasets.")
    else:
        print("\n❌ Test failed. Check the implementation.")
        sys.exit(1)
