{% extends "base.html" %}

{% block title %}DMP Generator - Home{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="text-center mb-5">
            <h1 class="display-4 mb-3">
                <i class="fas fa-file-alt text-primary"></i>
                Data Management Plan Generator
            </h1>
            <p class="lead">Create and validate Data Management Plans following the RDA-DMP Common Standard</p>
        </div>

        <div class="row g-4">
            <div class="col-md-6">
                <div class="card h-100 shadow-sm">
                    <div class="card-body text-center">
                        <i class="fas fa-plus-circle fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">Create New DMP</h5>
                        <p class="card-text">Generate a new Data Management Plan by filling out a user-friendly form with all required information.</p>
                        <a href="{{ url_for('create_dmp') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create DMP
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card h-100 shadow-sm">
                    <div class="card-body text-center">
                        <i class="fas fa-eye fa-3x text-success mb-3"></i>
                        <h5 class="card-title">View Examples</h5>
                        <p class="card-text">Explore example DMPs to understand the structure and see what a complete DMP looks like.</p>
                        <a href="{{ url_for('examples') }}" class="btn btn-success">
                            <i class="fas fa-eye"></i> View Examples
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-5">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-upload text-warning"></i>
                        Validate Existing DMP
                    </h5>
                    <p class="card-text">Upload a DMP JSON file to validate it against the RDA-DMP Common Standard.</p>
                    
                    <form id="validateForm" enctype="multipart/form-data">
                        <div class="row align-items-end">
                            <div class="col-md-8">
                                <input type="file" class="form-control" id="dmpFile" accept=".json" required>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-warning w-100">
                                    <i class="fas fa-check-circle"></i> Validate
                                </button>
                            </div>
                        </div>
                    </form>
                    
                    <div id="validationResult" class="mt-3"></div>
                </div>
            </div>
        </div>

        <div class="mt-5">
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle"></i> About the RDA-DMP Common Standard</h6>
                <p class="mb-0">
                    This tool generates DMPs following the 
                    <a href="https://github.com/RDA-DMP-Common/RDA-DMP-Common-Standard" target="_blank">RDA-DMP Common Standard</a>,
                    which provides a structured approach to creating machine-actionable Data Management Plans.
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.getElementById('validateForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const fileInput = document.getElementById('dmpFile');
    const resultDiv = document.getElementById('validationResult');
    
    if (!fileInput.files[0]) {
        resultDiv.innerHTML = '<div class="alert alert-danger">Please select a file to validate.</div>';
        return;
    }
    
    const formData = new FormData();
    formData.append('file', fileInput.files[0]);
    
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin"></i> Validating...</div>';
    
    fetch('/validate_dmp', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultDiv.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> ${data.message}
                </div>
            `;
        } else {
            resultDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> ${data.error}
                </div>
            `;
        }
    })
    .catch(error => {
        resultDiv.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> Error: ${error.message}
            </div>
        `;
    });
});
</script>
{% endblock %}
